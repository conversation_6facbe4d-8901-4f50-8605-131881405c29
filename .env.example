# understand.me Environment Configuration
# Copy this file to .env and fill in your actual API keys

# Supabase Configuration (Required)
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# ElevenLabs Configuration (Required for Voice)
EXPO_PUBLIC_ELEVENLABS_API_KEY=your_elevenlabs_api_key
EXPO_PUBLIC_ELEVENLABS_AGENT_ID=your_udine_agent_id

# Hume AI Configuration (Required for Emotion Analysis)
EXPO_PUBLIC_HUME_API_KEY=your_hume_api_key
EXPO_PUBLIC_HUME_SECRET_KEY=your_hume_secret_key

# Google AI Configuration (Required for Gemini)
EXPO_PUBLIC_GOOGLE_AI_API_KEY=your_google_ai_api_key

# OpenAI Configuration (Optional, for fallback)
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_APP_VERSION=1.0.0

# Analytics (Optional)
EXPO_PUBLIC_ANALYTICS_ENABLED=false
EXPO_PUBLIC_MIXPANEL_TOKEN=your_mixpanel_token

# Push Notifications (Optional)
EXPO_PUBLIC_PUSH_NOTIFICATIONS_ENABLED=false

# Feature Flags
EXPO_PUBLIC_VOICE_ENABLED=true
EXPO_PUBLIC_EMOTION_ANALYSIS_ENABLED=true
EXPO_PUBLIC_AI_INSIGHTS_ENABLED=true
EXPO_PUBLIC_REAL_TIME_ENABLED=true

# Development Settings
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_LOG_LEVEL=debug
NODE_ENV=development
