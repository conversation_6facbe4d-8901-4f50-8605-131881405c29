{
  "recommendations": [
    // Essential for TypeScript/JavaScript development
    "ms-vscode.vscode-typescript-next",
    
    // ESLint integration
    "dbaeumer.vscode-eslint",
    
    // Prettier code formatting
    "esbenp.prettier-vscode",
    
    // React Native development
    "ms-vscode.vscode-react-native",
    "msjsdiag.vscode-react-native",
    
    // React development
    "burkeholland.simple-react-snippets",
    "dsznajder.es7-react-js-snippets",
    
    // TypeScript helpers
    "ms-vscode.vscode-typescript-next",
    "usernamehw.errorlens",
    
    // Git integration
    "eamodio.gitlens",
    
    // File icons
    "vscode-icons-team.vscode-icons",
    
    // Bracket pair colorization
    "coenraads.bracket-pair-colorizer-2",
    
    // Auto rename tag
    "formulahendry.auto-rename-tag",
    
    // Path intellisense
    "christian-kohler.path-intellisense",
    
    // Import cost
    "wix.vscode-import-cost",
    
    // Thunder Client for API testing
    "rangav.vscode-thunder-client",
    
    // Expo tools
    "expo.vscode-expo-tools"
  ]
}
