{
  // ESLint configuration
  "eslint.enable": true,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.run": "onType",
  "eslint.workingDirectories": ["."],
  
  // TypeScript configuration
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  
  // Editor configuration for React Native/TypeScript
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  
  // File associations
  "files.associations": {
    "*.tsx": "typescriptreact",
    "*.ts": "typescript"
  },
  
  // Emmet configuration for React
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },
  "emmet.triggerExpansionOnTab": true,
  
  // React Native specific settings
  "react-native-tools.showUserTips": false,
  "react-native-tools.projectRoot": "./",
  
  // Prettier integration (if you want to use it alongside ESLint)
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  
  // Search and file explorer settings
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.expo": true,
    "**/ios": true,
    "**/android": true,
    "**/web-build": true
  },
  "files.exclude": {
    "**/.expo": true,
    "**/node_modules": true
  },
  
  // Terminal settings
  "terminal.integrated.defaultProfile.linux": "bash",
  
  // Workspace trust
  "security.workspace.trust.untrustedFiles": "open"
}
