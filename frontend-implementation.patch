From 133920f19de343b888855a7f6690fc6e3119b5f6 Mon Sep 17 00:00:00 2001
From: "codegen-sh[bot]" <131295404+codegen-sh[bot]@users.noreply.github.com>
Date: Mon, 30 Jun 2025 13:06:06 +0000
Subject: [PATCH 1/2] feat: implement design system, core UI components, and
 authentication screens

- Add comprehensive design system (Colors, Typography, Spacing)
- Create core UI components (Button, Input, Card, ResponsiveLayout)
- Implement clean login and register screens
- Add enhanced auth store with onboarding support
- Create onboarding step wrapper component
- Add welcome onboarding screen
- Update package.json with required dependencies
---
 app/(auth)/login.tsx                     | 835 +++++------------------
 app/(auth)/onboarding/welcome.tsx        | 118 ++++
 app/(auth)/register.tsx                  | 303 ++++++++
 components/layout/ResponsiveLayout.tsx   |  70 ++
 components/onboarding/OnboardingStep.tsx | 155 +++++
 components/ui/Button.tsx                 | 181 +++++
 components/ui/Card.tsx                   |  66 ++
 components/ui/Input.tsx                  | 163 +++++
 constants/Colors.ts                      | 114 ++++
 constants/Spacing.ts                     |  84 +++
 constants/Typography.ts                  | 196 ++++++
 package.json                             |   6 +
 services/ai/orchestrationEngine.ts       |   4 +-
 stores/authStore.ts                      |  44 ++
 14 files changed, 1682 insertions(+), 657 deletions(-)
 create mode 100644 app/(auth)/onboarding/welcome.tsx
 create mode 100644 app/(auth)/register.tsx
 create mode 100644 components/layout/ResponsiveLayout.tsx
 create mode 100644 components/onboarding/OnboardingStep.tsx
 create mode 100644 components/ui/Button.tsx
 create mode 100644 components/ui/Card.tsx
 create mode 100644 components/ui/Input.tsx
 create mode 100644 constants/Colors.ts
 create mode 100644 constants/Spacing.ts
 create mode 100644 constants/Typography.ts

diff --git a/app/(auth)/login.tsx b/app/(auth)/login.tsx
index a6c5bf8..e68faaf 100644
--- a/app/(auth)/login.tsx
+++ b/app/(auth)/login.tsx
@@ -1,493 +1,172 @@
-import React, { useState, useEffect } from 'react';
+import React, { useState } from 'react';
 import {
   View,
   Text,
-  TextInput,
-  TouchableOpacity,
   StyleSheet,
-  Platform,
-  Alert,
   KeyboardAvoidingView,
+  Platform,
   ScrollView,
+  Alert,
+  TouchableOpacity,
 } from 'react-native';
-import { LinearGradient } from 'expo-linear-gradient';
 import { router } from 'expo-router';
-import { User, Lock, Mic, MessageCircle, Play, Mail } from 'lucide-react-native';
-import { VoiceInteractionCore } from '../../components/VoiceInteractionCore';
-import { useOnboardingStore } from '../../stores/onboardingStore';
-import { useAuthStore } from '../../stores/authStore';
-import { supabase } from '../../lib/supabase';
+import { Mail, Lock, Eye, EyeOff } from 'lucide-react-native';
 
-const PERSONALITY_QUESTIONS = [
-  "When a disagreement arises, do you prefer to address it immediately or take time to think?",
-  "How do you typically express your feelings during conflicts?",
-  "What's most important to you when resolving disagreements - being heard or finding a solution?",
-  "Do you prefer direct communication or a more gentle approach when discussing sensitive topics?",
-  "How do you usually feel after resolving a conflict with someone close to you?"
-];
+import { ResponsiveLayout } from '../../components/layout/ResponsiveLayout';
+import { Card } from '../../components/ui/Card';
+import { Button } from '../../components/ui/Button';
+import { Input } from '../../components/ui/Input';
+import { useAuthStore } from '../../stores/authStore';
+import { Colors } from '../../constants/Colors';
+import { Typography } from '../../constants/Typography';
+import { Spacing } from '../../constants/Spacing';
 
 export default function LoginScreen() {
+  const [email, setEmail] = useState('');
   const [password, setPassword] = useState('');
-  const [currentVoiceInput, setCurrentVoiceInput] = useState('');
-  const [isListening, setIsListening] = useState(false);
-  const [isSpeaking, setIsSpeaking] = useState(false);
-  const [isThinking, setIsThinking] = useState(false);
-  const [showInputs, setShowInputs] = useState(false);
-  const [authError, setAuthError] = useState('');
-
-  const { isLoading, isSignUp, setLoading, toggleAuthMode } = useAuthStore();
-
-  const {
-    name,
-    email,
-    currentStep,
-    personalityAnswers,
-    currentQuestionIndex,
-    setName,
-    setEmail,
-    addPersonalityAnswer,
-    setCurrentStep,
-    nextQuestion,
-    reset,
-  } = useOnboardingStore();
-
-  useEffect(() => {
-    // Start the voice interaction when component mounts
-    if (isSignUp) {
-      startVoiceGreeting();
-    } else {
-      // For returning users, show inputs immediately
-      setShowInputs(true);
-      setCurrentStep('email');
-    }
-  }, [isSignUp]);
-
-  const startVoiceGreeting = async () => {
-    setIsSpeaking(true);
-    // Simulate AI speaking
-    await new Promise(resolve => setTimeout(resolve, 2000));
-    setIsSpeaking(false);
-    setCurrentStep('name');
-    startListening();
-  };
+  const [showPassword, setShowPassword] = useState(false);
+  const [emailError, setEmailError] = useState('');
+  const [passwordError, setPasswordError] = useState('');
 
-  const startListening = () => {
-    setIsListening(true);
-    // Simulate voice input after 3 seconds
-    setTimeout(() => {
-      setIsListening(false);
-      setIsThinking(true);
-      setTimeout(() => {
-        setIsThinking(false);
-        handleVoiceInput();
-      }, 1000);
-    }, 3000);
-  };
-
-  const handleVoiceInput = () => {
-    if (currentStep === 'name') {
-      const mockName = 'John Doe';
-      setName(mockName);
-      setCurrentVoiceInput(mockName);
-      setCurrentStep('email');
-      setTimeout(() => {
-        setIsSpeaking(true);
-        setTimeout(() => {
-          setIsSpeaking(false);
-          startListening();
-        }, 1500);
-      }, 500);
-    } else if (currentStep === 'email') {
-      const mockEmail = '<EMAIL>';
-      setEmail(mockEmail);
-      setCurrentVoiceInput(mockEmail);
-      setShowInputs(true);
-      setCurrentStep('password');
-      setTimeout(() => {
-        setIsSpeaking(true);
-        setTimeout(() => {
-          setIsSpeaking(false);
-        }, 2000);
-      }, 500);
-    } else if (currentStep === 'personality') {
-      const mockAnswer = 'I prefer to take time to think before addressing conflicts.';
-      addPersonalityAnswer(PERSONALITY_QUESTIONS[currentQuestionIndex], mockAnswer);
-      
-      if (currentQuestionIndex < PERSONALITY_QUESTIONS.length - 1) {
-        nextQuestion();
-        setTimeout(() => {
-          setIsSpeaking(true);
-          setTimeout(() => {
-            setIsSpeaking(false);
-            startListening();
-          }, 2000);
-        }, 500);
-      } else {
-        setCurrentStep('complete');
-        setTimeout(() => {
-          router.replace('/(main)');
-        }, 2000);
-      }
-    }
-  };
-
-  const handleSignUp = async () => {
-    if (!name || !email || !password) {
-      setAuthError('Please fill in all fields');
-      return;
-    }
-
-    setLoading(true);
-    setAuthError('');
-    
-    try {
-      const { data, error } = await supabase.auth.signUp({
-        email,
-        password,
-        options: {
-          data: {
-            full_name: name,
-          },
-        },
-      });
-
-      if (error) throw error;
+  const { login, isLoading } = useAuthStore();
 
-      if (data.user) {
-        // Check if email confirmation is required
-        if (!data.session) {
-          Alert.alert(
-            'Check your email',
-            'We sent you a confirmation link. Please check your email and click the link to verify your account.',
-            [{ text: 'OK' }]
-          );
-        } else {
-          // User is signed up and logged in, proceed to personality assessment
-          setCurrentStep('personality');
-          setIsSpeaking(true);
-          setTimeout(() => {
-            setIsSpeaking(false);
-            startListening();
-          }, 2000);
-        }
-      }
-    } catch (error: any) {
-      setAuthError(error.message);
-    } finally {
-      setLoading(false);
-    }
+  const validateEmail = (email: string) => {
+    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
+    return emailRegex.test(email);
   };
 
-  const handleSignIn = async () => {
-    if (!email || !password) {
-      setAuthError('Please enter your email and password');
-      return;
-    }
+  const handleLogin = async () => {
+    // Reset errors
+    setEmailError('');
+    setPasswordError('');
 
-    setLoading(true);
-    setAuthError('');
-    
-    try {
-      const { data, error } = await supabase.auth.signInWithPassword({
-        email,
-        password,
-      });
-
-      if (error) throw error;
+    // Validation
+    let hasErrors = false;
 
-      if (data.user) {
-        // User is signed in, redirect to main app
-        router.replace('/(main)');
-      }
-    } catch (error: any) {
-      setAuthError(error.message);
-    } finally {
-      setLoading(false);
+    if (!email) {
+      setEmailError('Email is required');
+      hasErrors = true;
+    } else if (!validateEmail(email)) {
+      setEmailError('Please enter a valid email address');
+      hasErrors = true;
     }
-  };
 
-  const handleGoogleSignIn = async () => {
-    setLoading(true);
-    setAuthError('');
-    
-    try {
-      const { data, error } = await supabase.auth.signInWithOAuth({
-        provider: 'google',
-        options: {
-          redirectTo: Platform.OS === 'web' 
-            ? `${window.location.origin}/(main)` 
-            : 'understand-me:///(main)',
-        },
-      });
-
-      if (error) throw error;
-    } catch (error: any) {
-      setAuthError(error.message);
-    } finally {
-      setLoading(false);
+    if (!password) {
+      setPasswordError('Password is required');
+      hasErrors = true;
+    } else if (password.length < 6) {
+      setPasswordError('Password must be at least 6 characters');
+      hasErrors = true;
     }
-  };
 
-  const handleAppleSignIn = async () => {
-    if (Platform.OS !== 'ios') {
-      setAuthError('Apple Sign In is only available on iOS');
-      return;
-    }
+    if (hasErrors) return;
 
-    setLoading(true);
-    setAuthError('');
-    
     try {
-      const { data, error } = await supabase.auth.signInWithOAuth({
-        provider: 'apple',
-        options: {
-          redirectTo: 'understand-me:///(main)',
-        },
-      });
-
-      if (error) throw error;
-    } catch (error: any) {
-      setAuthError(error.message);
-    } finally {
-      setLoading(false);
-    }
-  };
-
-  const getCurrentQuestion = () => {
-    return PERSONALITY_QUESTIONS[currentQuestionIndex];
-  };
-
-  const getStepText = () => {
-    if (!isSignUp) {
-      return "Welcome back!\n\nLet's continue your journey.";
-    }
-
-    switch (currentStep) {
-      case 'greeting':
-        return "Hello, I am Udine\n\nLet us start by getting to know you better?";
-      case 'name':
-        return "What's your name?";
-      case 'email':
-        return "What's your email address?";
-      case 'password':
-        return "Please create a secure password";
-      case 'personality':
-        return getCurrentQuestion();
-      case 'complete':
-        return "Thank you! Let's begin your journey.";
-      default:
-        return "Let us start by getting to know you better?";
+      await login(email, password);
+      router.replace('/(main)/dashboard');
+    } catch (error) {
+      Alert.alert('Login Failed', 'Please check your credentials and try again.');
     }
   };
 
-  const getProgressText = () => {
-    if (currentStep === 'personality') {
-      return `${currentQuestionIndex + 1}/${PERSONALITY_QUESTIONS.length}`;
-    }
-    return '';
+  const handleSignUp = () => {
+    router.push('/(auth)/register');
   };
 
-  const shouldShowVoiceOrb = () => {
-    return isSignUp && (isListening || isSpeaking || isThinking);
+  const handleForgotPassword = () => {
+    // TODO: Implement forgot password
+    Alert.alert('Forgot Password', 'This feature will be available soon.');
   };
 
   return (
-    <LinearGradient
-      colors={['#4ECDC4', '#44A08D', '#45B7D1']}
-      style={styles.container}
-      start={{ x: 0, y: 0 }}
-      end={{ x: 1, y: 1 }}
-    >
+    <ResponsiveLayout>
       <KeyboardAvoidingView
-        style={styles.keyboardAvoid}
+        style={styles.container}
         behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
       >
-        <ScrollView contentContainerStyle={styles.scrollContent}>
-          {/* Header */}
+        <ScrollView
+          contentContainerStyle={styles.scrollContent}
+          showsVerticalScrollIndicator={false}
+        >
           <View style={styles.header}>
-            <Text style={styles.appName}>Understand{'\n'}.me</Text>
-            <TouchableOpacity 
-              style={styles.menuButton}
-              onPress={toggleAuthMode}
-            >
-              <Text style={styles.menuText}>
-                {isSignUp ? 'Sign In' : 'Sign Up'}
-              </Text>
-            </TouchableOpacity>
-          </View>
-
-          {/* Main Content */}
-          <View style={styles.mainContent}>
+            <Text style={styles.title}>Welcome Back</Text>
             <Text style={styles.subtitle}>
-              {isSignUp ? 'Create Account' : 'Welcome back'}
+              Sign in to continue your conflict resolution journey
             </Text>
-            
-            <Text style={styles.mainText}>{getStepText()}</Text>
-
-            {/* Voice Interaction Orb - Only for sign up flow */}
-            {shouldShowVoiceOrb() && (
-              <View style={styles.orbContainer}>
-                <VoiceInteractionCore
-                  isListening={isListening}
-                  isSpeaking={isSpeaking}
-                  isThinking={isThinking}
-                />
-              </View>
-            )}
-
-            {/* Progress Indicator */}
-            {getProgressText() && (
-              <View style={styles.progressContainer}>
-                <View style={styles.progressCircle}>
-                  <Text style={styles.progressText}>{getProgressText()}</Text>
-                </View>
-              </View>
-            )}
-
-            {/* Input Card */}
-            <View style={styles.inputCard}>
-              {/* Error Message */}
-              {authError ? (
-                <View style={styles.errorContainer}>
-                  <Text style={styles.errorText}>{authError}</Text>
-                </View>
-              ) : null}
-
-              {/* Name Input - Only for sign up */}
-              {isSignUp && (currentStep === 'name' && name || showInputs) && (
-                <View style={styles.inputRow}>
-                  <User size={20} color="#666" />
-                  <TextInput
-                    style={styles.input}
-                    placeholder="Full name"
-                    value={name}
-                    onChangeText={setName}
-                    autoCapitalize="words"
-                    editable={!name || showInputs}
-                  />
-                </View>
-              )}
-
-              {/* Email Input */}
-              {(currentStep === 'email' && email || showInputs || !isSignUp) && (
-                <View style={styles.inputRow}>
-                  <Mail size={20} color="#666" />
-                  <TextInput
-                    style={styles.input}
-                    placeholder="Email address"
-                    value={email}
-                    onChangeText={setEmail}
-                    keyboardType="email-address"
-                    autoCapitalize="none"
-                    editable={!email || showInputs || !isSignUp}
-                  />
-                </View>
-              )}
-
-              {/* Password Input */}
-              {(showInputs || !isSignUp) && (
-                <View style={styles.inputRow}>
-                  <Lock size={20} color="#666" />
-                  <TextInput
-                    style={styles.input}
-                    placeholder={isSignUp ? "Create password" : "Password"}
-                    value={password}
-                    onChangeText={setPassword}
-                    secureTextEntry
-                    autoCapitalize="none"
-                  />
-                </View>
-              )}
-
-              {/* Social Login Section */}
-              {(showInputs || !isSignUp) && (
-                <>
-                  <Text style={styles.orText}>OR</Text>
-
-                  <View style={styles.socialButtons}>
-                    <TouchableOpacity 
-                      style={styles.socialButton}
-                      onPress={handleGoogleSignIn}
-                      disabled={isLoading}
-                    >
-                      <Text style={styles.socialButtonText}>G</Text>
-                    </TouchableOpacity>
-                    
-                    {Platform.OS === 'ios' && (
-                      <TouchableOpacity 
-                        style={styles.socialButton}
-                        onPress={handleAppleSignIn}
-                        disabled={isLoading}
-                      >
-                        <Text style={styles.socialButtonText}>🍎</Text>
-                      </TouchableOpacity>
-                    )}
-                  </View>
-                </>
-              )}
+          </View>
 
-              {/* Voice Input for Personality Questions */}
-              {currentStep === 'personality' && (
-                <View style={styles.voiceInputContainer}>
-                  <Text style={styles.voiceInputLabel}>Type response or use voice</Text>
-                  <TouchableOpacity 
-                    style={styles.voiceButton}
-                    onPress={startListening}
+          <Card style={styles.card}>
+            <View style={styles.form}>
+              <Input
+                label="Email"
+                value={email}
+                onChangeText={setEmail}
+                placeholder="Enter your email"
+                keyboardType="email-address"
+                autoCapitalize="none"
+                autoComplete="email"
+                error={emailError}
+                leftIcon={<Mail size={20} color={Colors.text.tertiary} />}
+              />
+
+              <Input
+                label="Password"
+                value={password}
+                onChangeText={setPassword}
+                placeholder="Enter your password"
+                secureTextEntry={!showPassword}
+                autoComplete="password"
+                error={passwordError}
+                leftIcon={<Lock size={20} color={Colors.text.tertiary} />}
+                rightIcon={
+                  <TouchableOpacity
+                    onPress={() => setShowPassword(!showPassword)}
+                    style={styles.eyeButton}
                   >
-                    <Mic size={20} color="#666" />
+                    {showPassword ? (
+                      <EyeOff size={20} color={Colors.text.tertiary} />
+                    ) : (
+                      <Eye size={20} color={Colors.text.tertiary} />
+                    )}
                   </TouchableOpacity>
-                </View>
-              )}
-            </View>
-
-            {/* Action Buttons */}
-            <View style={styles.actionButtons}>
-              {(showInputs || !isSignUp) && (
-                <TouchableOpacity
-                  style={[styles.continueButton, isLoading && styles.disabledButton]}
-                  onPress={isSignUp ? handleSignUp : handleSignIn}
-                  disabled={isLoading}
-                >
-                  <Text style={styles.continueButtonText}>
-                    {isLoading 
-                      ? (isSignUp ? 'Creating Account...' : 'Signing In...') 
-                      : (isSignUp ? 'Continue the Journey' : 'Welcome Back')
-                    }
-                  </Text>
-                </TouchableOpacity>
-              )}
+                }
+              />
 
-              {/* Mode Toggle Button */}
-              <TouchableOpacity 
-                style={styles.newUserButton}
-                onPress={toggleAuthMode}
-              >
-                <User size={20} color="#fff" />
-                <Text style={styles.newUserText}>
-                  {isSignUp ? 'Sign In' : 'Sign Up'}
-                </Text>
+              <TouchableOpacity onPress={handleForgotPassword} style={styles.forgotPassword}>
+                <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
               </TouchableOpacity>
-            </View>
 
-            {/* Bottom Controls */}
-            <View style={styles.bottomControls}>
-              <TouchableOpacity style={styles.controlButton}>
-                <MessageCircle size={24} color="#fff" />
-              </TouchableOpacity>
-              <TouchableOpacity 
-                style={styles.controlButton}
-                onPress={isSignUp ? startListening : undefined}
-              >
-                <Mic size={24} color="#fff" />
-              </TouchableOpacity>
-              <TouchableOpacity style={styles.controlButton}>
-                <Play size={24} color="#fff" />
-              </TouchableOpacity>
+              <Button
+                title="Sign In"
+                onPress={handleLogin}
+                loading={isLoading}
+                style={styles.loginButton}
+                fullWidth
+              />
+
+              <View style={styles.divider}>
+                <View style={styles.dividerLine} />
+                <Text style={styles.dividerText}>or</Text>
+                <View style={styles.dividerLine} />
+              </View>
+
+              <Button
+                title="Create New Account"
+                onPress={handleSignUp}
+                variant="outline"
+                fullWidth
+              />
             </View>
+          </Card>
+
+          <View style={styles.footer}>
+            <Text style={styles.footerText}>
+              By signing in, you agree to our Terms of Service and Privacy Policy
+            </Text>
           </View>
         </ScrollView>
       </KeyboardAvoidingView>
-    </LinearGradient>
+    </ResponsiveLayout>
   );
 }
 
@@ -495,241 +174,87 @@ const styles = StyleSheet.create({
   container: {
     flex: 1,
   },
-  keyboardAvoid: {
-    flex: 1,
-  },
+  
   scrollContent: {
     flexGrow: 1,
-    paddingHorizontal: 20,
-    paddingTop: Platform.OS === 'ios' ? 60 : 40,
-    paddingBottom: 40,
+    justifyContent: 'center',
+    paddingVertical: Spacing.xl,
   },
+  
   header: {
-    flexDirection: 'row',
-    justifyContent: 'space-between',
-    alignItems: 'flex-start',
-    marginBottom: 40,
-  },
-  appName: {
-    fontSize: 28,
-    fontWeight: '300',
-    color: '#fff',
-    lineHeight: 32,
-    fontFamily: 'Inter-Regular',
-  },
-  menuButton: {
-    paddingHorizontal: 16,
-    paddingVertical: 8,
-    borderRadius: 20,
-    backgroundColor: 'rgba(255, 255, 255, 0.2)',
-    borderWidth: 1,
-    borderColor: 'rgba(255, 255, 255, 0.3)',
-  },
-  menuText: {
-    color: '#fff',
-    fontSize: 14,
-    fontWeight: '600',
-    fontFamily: 'Inter-SemiBold',
-  },
-  mainContent: {
-    flex: 1,
     alignItems: 'center',
+    marginBottom: Spacing.xl,
   },
-  subtitle: {
-    fontSize: 16,
-    color: 'rgba(255, 255, 255, 0.8)',
-    marginBottom: 20,
+  
+  title: {
+    ...Typography.styles.h1,
+    color: Colors.text.primary,
     textAlign: 'center',
-    fontFamily: 'Inter-Regular',
+    marginBottom: Spacing.sm,
   },
-  mainText: {
-    fontSize: 24,
-    fontWeight: '600',
-    color: '#fff',
+  
+  subtitle: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
     textAlign: 'center',
-    marginBottom: 40,
-    lineHeight: 32,
-    paddingHorizontal: 20,
-    fontFamily: 'Inter-SemiBold',
+    maxWidth: 300,
   },
-  orbContainer: {
-    marginBottom: 30,
+  
+  card: {
+    marginHorizontal: Spacing.md,
+    marginBottom: Spacing.xl,
   },
-  progressContainer: {
-    marginBottom: 20,
+  
+  form: {
+    gap: Spacing.md,
   },
-  progressCircle: {
-    width: 60,
-    height: 60,
-    borderRadius: 30,
-    backgroundColor: 'rgba(255, 255, 255, 0.2)',
-    justifyContent: 'center',
-    alignItems: 'center',
-    borderWidth: 2,
-    borderColor: '#4ECDC4',
+  
+  eyeButton: {
+    padding: Spacing.xs,
   },
-  progressText: {
-    color: '#fff',
-    fontSize: 14,
-    fontWeight: '600',
-    fontFamily: 'Inter-SemiBold',
+  
+  forgotPassword: {
+    alignSelf: 'flex-end',
+    marginTop: -Spacing.sm,
   },
-  inputCard: {
-    backgroundColor: 'rgba(255, 255, 255, 0.95)',
-    borderRadius: 20,
-    padding: 20,
-    width: '100%',
-    marginBottom: 30,
-    shadowColor: '#000',
-    shadowOffset: {
-      width: 0,
-      height: 4,
-    },
-    shadowOpacity: 0.1,
-    shadowRadius: 8,
-    elevation: 8,
+  
+  forgotPasswordText: {
+    ...Typography.styles.caption,
+    color: Colors.primary[500],
   },
-  errorContainer: {
-    backgroundColor: '#ffebee',
-    borderRadius: 8,
-    padding: 12,
-    marginBottom: 15,
-    borderLeftWidth: 4,
-    borderLeftColor: '#f44336',
+  
+  loginButton: {
+    marginTop: Spacing.sm,
   },
-  errorText: {
-    color: '#c62828',
-    fontSize: 14,
-    fontFamily: 'Inter-Regular',
-  },
-  inputRow: {
+  
+  divider: {
     flexDirection: 'row',
     alignItems: 'center',
-    paddingVertical: 15,
-    borderBottomWidth: 1,
-    borderBottomColor: '#f0f0f0',
+    marginVertical: Spacing.lg,
   },
-  input: {
-    fontSize: 16,
-    color: '#333',
-    marginLeft: 10,
+  
+  dividerLine: {
     flex: 1,
-    paddingVertical: 0,
-    fontFamily: 'Inter-Regular',
+    height: 1,
+    backgroundColor: Colors.border.light,
   },
-  orText: {
-    textAlign: 'center',
-    color: '#999',
-    fontSize: 14,
-    marginVertical: 20,
-    fontFamily: 'Inter-Regular',
-  },
-  socialButtons: {
-    flexDirection: 'row',
-    justifyContent: 'center',
-    gap: 15,
+  
+  dividerText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+    marginHorizontal: Spacing.md,
   },
-  socialButton: {
-    width: 50,
-    height: 50,
-    borderRadius: 25,
-    backgroundColor: '#f8f8f8',
-    justifyContent: 'center',
-    alignItems: 'center',
-    shadowColor: '#000',
-    shadowOffset: {
-      width: 0,
-      height: 2,
-    },
-    shadowOpacity: 0.1,
-    shadowRadius: 4,
-    elevation: 4,
-  },
-  socialButtonText: {
-    fontSize: 20,
-    fontWeight: '600',
-  },
-  voiceInputContainer: {
-    flexDirection: 'row',
-    alignItems: 'center',
-    justifyContent: 'space-between',
-    paddingVertical: 15,
-  },
-  voiceInputLabel: {
-    fontSize: 16,
-    color: '#999',
-    flex: 1,
-    fontFamily: 'Inter-Regular',
-  },
-  voiceButton: {
-    width: 40,
-    height: 40,
-    borderRadius: 20,
-    backgroundColor: '#f0f0f0',
-    justifyContent: 'center',
-    alignItems: 'center',
-  },
-  actionButtons: {
-    width: '100%',
-    alignItems: 'center',
-    gap: 15,
-  },
-  continueButton: {
-    backgroundColor: '#2C3E50',
-    borderRadius: 25,
-    paddingVertical: 15,
-    paddingHorizontal: 40,
-    width: '100%',
+  
+  footer: {
     alignItems: 'center',
-    shadowColor: '#000',
-    shadowOffset: {
-      width: 0,
-      height: 2,
-    },
-    shadowOpacity: 0.1,
-    shadowRadius: 4,
-    elevation: 4,
+    paddingHorizontal: Spacing.xl,
   },
-  disabledButton: {
-    opacity: 0.6,
-  },
-  continueButtonText: {
-    color: '#fff',
-    fontSize: 16,
-    fontWeight: '600',
-    fontFamily: 'Inter-SemiBold',
-  },
-  newUserButton: {
-    flexDirection: 'row',
-    alignItems: 'center',
-    paddingHorizontal: 20,
-    paddingVertical: 12,
-    borderRadius: 25,
-    backgroundColor: 'rgba(255, 255, 255, 0.2)',
-    borderWidth: 2,
-    borderColor: 'rgba(255, 255, 255, 0.3)',
-  },
-  newUserText: {
-    color: '#fff',
-    fontSize: 16,
-    fontWeight: '500',
-    marginLeft: 8,
-    fontFamily: 'Inter-Regular',
-  },
-  bottomControls: {
-    flexDirection: 'row',
-    justifyContent: 'center',
-    gap: 30,
-    marginTop: 40,
-  },
-  controlButton: {
-    width: 50,
-    height: 50,
-    borderRadius: 25,
-    backgroundColor: 'rgba(255, 255, 255, 0.2)',
-    justifyContent: 'center',
-    alignItems: 'center',
-    borderWidth: 1,
-    borderColor: 'rgba(255, 255, 255, 0.3)',
+  
+  footerText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+    textAlign: 'center',
+    lineHeight: 18,
   },
-});
\ No newline at end of file
+});
+
diff --git a/app/(auth)/onboarding/welcome.tsx b/app/(auth)/onboarding/welcome.tsx
new file mode 100644
index 0000000..36504d8
--- /dev/null
+++ b/app/(auth)/onboarding/welcome.tsx
@@ -0,0 +1,118 @@
+import React from 'react';
+import { View, Text, StyleSheet } from 'react-native';
+import { router } from 'expo-router';
+import { Heart, Users, Target } from 'lucide-react-native';
+
+import { OnboardingStep } from '../../../components/onboarding/OnboardingStep';
+import { Colors } from '../../../constants/Colors';
+import { Typography } from '../../../constants/Typography';
+import { Spacing } from '../../../constants/Spacing';
+
+export default function WelcomeScreen() {
+  const handleNext = () => {
+    router.push('/(auth)/onboarding/username');
+  };
+
+  return (
+    <OnboardingStep
+      title="Welcome to Understand.me"
+      subtitle="Let's set up your profile to personalize your conflict resolution journey"
+      currentStep={1}
+      totalSteps={7}
+      onNext={handleNext}
+      nextButtonText="Get Started"
+    >
+      <View style={styles.content}>
+        <View style={styles.features}>
+          <View style={styles.feature}>
+            <View style={styles.iconContainer}>
+              <Heart size={32} color={Colors.emotion.positive} />
+            </View>
+            <Text style={styles.featureTitle}>Emotional Intelligence</Text>
+            <Text style={styles.featureDescription}>
+              Learn to understand and manage emotions during conflicts
+            </Text>
+          </View>
+
+          <View style={styles.feature}>
+            <View style={styles.iconContainer}>
+              <Users size={32} color={Colors.primary[500]} />
+            </View>
+            <Text style={styles.featureTitle}>Better Communication</Text>
+            <Text style={styles.featureDescription}>
+              Develop skills for healthier conversations and relationships
+            </Text>
+          </View>
+
+          <View style={styles.feature}>
+            <View style={styles.iconContainer}>
+              <Target size={32} color={Colors.secondary[500]} />
+            </View>
+            <Text style={styles.featureTitle}>Personalized Growth</Text>
+            <Text style={styles.featureDescription}>
+              Get insights tailored to your communication style and goals
+            </Text>
+          </View>
+        </View>
+
+        <View style={styles.welcomeMessage}>
+          <Text style={styles.welcomeText}>
+            We'll guide you through a few quick questions to create your personalized experience.
+          </Text>
+        </View>
+      </View>
+    </OnboardingStep>
+  );
+}
+
+const styles = StyleSheet.create({
+  content: {
+    flex: 1,
+    justifyContent: 'space-between',
+  },
+  
+  features: {
+    gap: Spacing.xl,
+  },
+  
+  feature: {
+    alignItems: 'center',
+    paddingHorizontal: Spacing.md,
+  },
+  
+  iconContainer: {
+    width: 80,
+    height: 80,
+    borderRadius: 40,
+    backgroundColor: Colors.background.tertiary,
+    justifyContent: 'center',
+    alignItems: 'center',
+    marginBottom: Spacing.md,
+  },
+  
+  featureTitle: {
+    ...Typography.styles.h5,
+    color: Colors.text.primary,
+    textAlign: 'center',
+    marginBottom: Spacing.sm,
+  },
+  
+  featureDescription: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+    textAlign: 'center',
+    lineHeight: 22,
+  },
+  
+  welcomeMessage: {
+    marginTop: Spacing.xl,
+    paddingHorizontal: Spacing.md,
+  },
+  
+  welcomeText: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+    textAlign: 'center',
+    lineHeight: 22,
+  },
+});
diff --git a/app/(auth)/register.tsx b/app/(auth)/register.tsx
new file mode 100644
index 0000000..f2c88e0
--- /dev/null
+++ b/app/(auth)/register.tsx
@@ -0,0 +1,303 @@
+import React, { useState } from 'react';
+import {
+  View,
+  Text,
+  StyleSheet,
+  KeyboardAvoidingView,
+  Platform,
+  ScrollView,
+  Alert,
+  TouchableOpacity,
+} from 'react-native';
+import { router } from 'expo-router';
+import { Mail, Lock, Eye, EyeOff, User } from 'lucide-react-native';
+
+import { ResponsiveLayout } from '../../components/layout/ResponsiveLayout';
+import { Card } from '../../components/ui/Card';
+import { Button } from '../../components/ui/Button';
+import { Input } from '../../components/ui/Input';
+import { useAuthStore } from '../../stores/authStore';
+import { Colors } from '../../constants/Colors';
+import { Typography } from '../../constants/Typography';
+import { Spacing } from '../../constants/Spacing';
+
+export default function RegisterScreen() {
+  const [name, setName] = useState('');
+  const [email, setEmail] = useState('');
+  const [password, setPassword] = useState('');
+  const [confirmPassword, setConfirmPassword] = useState('');
+  const [showPassword, setShowPassword] = useState(false);
+  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
+  
+  const [nameError, setNameError] = useState('');
+  const [emailError, setEmailError] = useState('');
+  const [passwordError, setPasswordError] = useState('');
+  const [confirmPasswordError, setConfirmPasswordError] = useState('');
+
+  const { signup, isLoading } = useAuthStore();
+
+  const validateEmail = (email: string) => {
+    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
+    return emailRegex.test(email);
+  };
+
+  const handleRegister = async () => {
+    // Reset errors
+    setNameError('');
+    setEmailError('');
+    setPasswordError('');
+    setConfirmPasswordError('');
+
+    // Validation
+    let hasErrors = false;
+
+    if (!name.trim()) {
+      setNameError('Name is required');
+      hasErrors = true;
+    } else if (name.trim().length < 2) {
+      setNameError('Name must be at least 2 characters');
+      hasErrors = true;
+    }
+
+    if (!email) {
+      setEmailError('Email is required');
+      hasErrors = true;
+    } else if (!validateEmail(email)) {
+      setEmailError('Please enter a valid email address');
+      hasErrors = true;
+    }
+
+    if (!password) {
+      setPasswordError('Password is required');
+      hasErrors = true;
+    } else if (password.length < 8) {
+      setPasswordError('Password must be at least 8 characters');
+      hasErrors = true;
+    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
+      setPasswordError('Password must contain uppercase, lowercase, and number');
+      hasErrors = true;
+    }
+
+    if (!confirmPassword) {
+      setConfirmPasswordError('Please confirm your password');
+      hasErrors = true;
+    } else if (password !== confirmPassword) {
+      setConfirmPasswordError('Passwords do not match');
+      hasErrors = true;
+    }
+
+    if (hasErrors) return;
+
+    try {
+      await signup(email, password, name.trim());
+      // Navigate to onboarding flow
+      router.replace('/(auth)/onboarding/welcome');
+    } catch (error) {
+      Alert.alert('Registration Failed', 'Please try again.');
+    }
+  };
+
+  const handleSignIn = () => {
+    router.push('/(auth)/login');
+  };
+
+  return (
+    <ResponsiveLayout>
+      <KeyboardAvoidingView
+        style={styles.container}
+        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
+      >
+        <ScrollView
+          contentContainerStyle={styles.scrollContent}
+          showsVerticalScrollIndicator={false}
+        >
+          <View style={styles.header}>
+            <Text style={styles.title}>Create Account</Text>
+            <Text style={styles.subtitle}>
+              Join understand.me and start your journey to better communication
+            </Text>
+          </View>
+
+          <Card style={styles.card}>
+            <View style={styles.form}>
+              <Input
+                label="Full Name"
+                value={name}
+                onChangeText={setName}
+                placeholder="Enter your full name"
+                autoCapitalize="words"
+                autoComplete="name"
+                error={nameError}
+                leftIcon={<User size={20} color={Colors.text.tertiary} />}
+              />
+
+              <Input
+                label="Email"
+                value={email}
+                onChangeText={setEmail}
+                placeholder="Enter your email"
+                keyboardType="email-address"
+                autoCapitalize="none"
+                autoComplete="email"
+                error={emailError}
+                leftIcon={<Mail size={20} color={Colors.text.tertiary} />}
+              />
+
+              <Input
+                label="Password"
+                value={password}
+                onChangeText={setPassword}
+                placeholder="Create a strong password"
+                secureTextEntry={!showPassword}
+                autoComplete="new-password"
+                error={passwordError}
+                hint="Must be 8+ characters with uppercase, lowercase, and number"
+                leftIcon={<Lock size={20} color={Colors.text.tertiary} />}
+                rightIcon={
+                  <TouchableOpacity
+                    onPress={() => setShowPassword(!showPassword)}
+                    style={styles.eyeButton}
+                  >
+                    {showPassword ? (
+                      <EyeOff size={20} color={Colors.text.tertiary} />
+                    ) : (
+                      <Eye size={20} color={Colors.text.tertiary} />
+                    )}
+                  </TouchableOpacity>
+                }
+              />
+
+              <Input
+                label="Confirm Password"
+                value={confirmPassword}
+                onChangeText={setConfirmPassword}
+                placeholder="Confirm your password"
+                secureTextEntry={!showConfirmPassword}
+                autoComplete="new-password"
+                error={confirmPasswordError}
+                leftIcon={<Lock size={20} color={Colors.text.tertiary} />}
+                rightIcon={
+                  <TouchableOpacity
+                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
+                    style={styles.eyeButton}
+                  >
+                    {showConfirmPassword ? (
+                      <EyeOff size={20} color={Colors.text.tertiary} />
+                    ) : (
+                      <Eye size={20} color={Colors.text.tertiary} />
+                    )}
+                  </TouchableOpacity>
+                }
+              />
+
+              <Button
+                title="Create Account"
+                onPress={handleRegister}
+                loading={isLoading}
+                style={styles.registerButton}
+                fullWidth
+              />
+
+              <View style={styles.divider}>
+                <View style={styles.dividerLine} />
+                <Text style={styles.dividerText}>or</Text>
+                <View style={styles.dividerLine} />
+              </View>
+
+              <Button
+                title="Sign In Instead"
+                onPress={handleSignIn}
+                variant="outline"
+                fullWidth
+              />
+            </View>
+          </Card>
+
+          <View style={styles.footer}>
+            <Text style={styles.footerText}>
+              By creating an account, you agree to our Terms of Service and Privacy Policy
+            </Text>
+          </View>
+        </ScrollView>
+      </KeyboardAvoidingView>
+    </ResponsiveLayout>
+  );
+}
+
+const styles = StyleSheet.create({
+  container: {
+    flex: 1,
+  },
+  
+  scrollContent: {
+    flexGrow: 1,
+    justifyContent: 'center',
+    paddingVertical: Spacing.xl,
+  },
+  
+  header: {
+    alignItems: 'center',
+    marginBottom: Spacing.xl,
+  },
+  
+  title: {
+    ...Typography.styles.h1,
+    color: Colors.text.primary,
+    textAlign: 'center',
+    marginBottom: Spacing.sm,
+  },
+  
+  subtitle: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+    textAlign: 'center',
+    maxWidth: 320,
+  },
+  
+  card: {
+    marginHorizontal: Spacing.md,
+    marginBottom: Spacing.xl,
+  },
+  
+  form: {
+    gap: Spacing.md,
+  },
+  
+  eyeButton: {
+    padding: Spacing.xs,
+  },
+  
+  registerButton: {
+    marginTop: Spacing.sm,
+  },
+  
+  divider: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    marginVertical: Spacing.lg,
+  },
+  
+  dividerLine: {
+    flex: 1,
+    height: 1,
+    backgroundColor: Colors.border.light,
+  },
+  
+  dividerText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+    marginHorizontal: Spacing.md,
+  },
+  
+  footer: {
+    alignItems: 'center',
+    paddingHorizontal: Spacing.xl,
+  },
+  
+  footerText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+    textAlign: 'center',
+    lineHeight: 18,
+  },
+});
diff --git a/components/layout/ResponsiveLayout.tsx b/components/layout/ResponsiveLayout.tsx
new file mode 100644
index 0000000..daa394b
--- /dev/null
+++ b/components/layout/ResponsiveLayout.tsx
@@ -0,0 +1,70 @@
+import React from 'react';
+import { View, StyleSheet, Dimensions, ViewStyle } from 'react-native';
+import { useSafeAreaInsets } from 'react-native-safe-area-context';
+import { Colors } from '../../constants/Colors';
+import { Spacing } from '../../constants/Spacing';
+
+const { width: screenWidth } = Dimensions.get('window');
+
+export interface ResponsiveLayoutProps {
+  children: React.ReactNode;
+  style?: ViewStyle;
+  padding?: boolean;
+  safeArea?: boolean;
+  maxWidth?: number;
+  centered?: boolean;
+}
+
+export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
+  children,
+  style,
+  padding = true,
+  safeArea = true,
+  maxWidth,
+  centered = false,
+}) => {
+  const insets = useSafeAreaInsets();
+  
+  const isTablet = screenWidth >= Spacing.breakpoints.tablet;
+  const isDesktop = screenWidth >= Spacing.breakpoints.desktop;
+  
+  const containerStyle = [
+    styles.container,
+    safeArea && {
+      paddingTop: insets.top,
+      paddingBottom: insets.bottom,
+      paddingLeft: insets.left,
+      paddingRight: insets.right,
+    },
+    padding && (isDesktop ? styles.desktopPadding : styles.mobilePadding),
+    maxWidth && { maxWidth },
+    centered && styles.centered,
+    style,
+  ];
+
+  return (
+    <View style={containerStyle}>
+      {children}
+    </View>
+  );
+};
+
+const styles = StyleSheet.create({
+  container: {
+    flex: 1,
+    backgroundColor: Colors.background.secondary,
+  },
+  
+  mobilePadding: {
+    paddingHorizontal: Spacing.component.screenPadding,
+  },
+  
+  desktopPadding: {
+    paddingHorizontal: Spacing.component.screenPaddingLarge,
+  },
+  
+  centered: {
+    alignItems: 'center',
+    justifyContent: 'center',
+  },
+});
diff --git a/components/onboarding/OnboardingStep.tsx b/components/onboarding/OnboardingStep.tsx
new file mode 100644
index 0000000..aff02d1
--- /dev/null
+++ b/components/onboarding/OnboardingStep.tsx
@@ -0,0 +1,155 @@
+import React from 'react';
+import { View, Text, StyleSheet } from 'react-native';
+import { ResponsiveLayout } from '../layout/ResponsiveLayout';
+import { Card } from '../ui/Card';
+import { Button } from '../ui/Button';
+import { Colors } from '../../constants/Colors';
+import { Typography } from '../../constants/Typography';
+import { Spacing } from '../../constants/Spacing';
+
+export interface OnboardingStepProps {
+  title: string;
+  subtitle?: string;
+  children: React.ReactNode;
+  currentStep: number;
+  totalSteps: number;
+  onNext?: () => void;
+  onBack?: () => void;
+  nextButtonText?: string;
+  backButtonText?: string;
+  nextDisabled?: boolean;
+  loading?: boolean;
+}
+
+export const OnboardingStep: React.FC<OnboardingStepProps> = ({
+  title,
+  subtitle,
+  children,
+  currentStep,
+  totalSteps,
+  onNext,
+  onBack,
+  nextButtonText = 'Continue',
+  backButtonText = 'Back',
+  nextDisabled = false,
+  loading = false,
+}) => {
+  const progress = (currentStep / totalSteps) * 100;
+
+  return (
+    <ResponsiveLayout>
+      <View style={styles.container}>
+        {/* Progress Bar */}
+        <View style={styles.progressContainer}>
+          <View style={styles.progressBackground}>
+            <View style={[styles.progressFill, { width: `${progress}%` }]} />
+          </View>
+          <Text style={styles.progressText}>
+            {currentStep} of {totalSteps}
+          </Text>
+        </View>
+
+        {/* Header */}
+        <View style={styles.header}>
+          <Text style={styles.title}>{title}</Text>
+          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
+        </View>
+
+        {/* Content */}
+        <Card style={styles.contentCard}>
+          {children}
+        </Card>
+
+        {/* Navigation Buttons */}
+        <View style={styles.navigation}>
+          {onBack && (
+            <Button
+              title={backButtonText}
+              onPress={onBack}
+              variant="outline"
+              style={styles.backButton}
+            />
+          )}
+          
+          {onNext && (
+            <Button
+              title={nextButtonText}
+              onPress={onNext}
+              disabled={nextDisabled}
+              loading={loading}
+              style={styles.nextButton}
+              fullWidth={!onBack}
+            />
+          )}
+        </View>
+      </View>
+    </ResponsiveLayout>
+  );
+};
+
+const styles = StyleSheet.create({
+  container: {
+    flex: 1,
+    paddingVertical: Spacing.xl,
+  },
+  
+  progressContainer: {
+    marginBottom: Spacing.xl,
+  },
+  
+  progressBackground: {
+    height: 4,
+    backgroundColor: Colors.border.light,
+    borderRadius: 2,
+    marginBottom: Spacing.sm,
+  },
+  
+  progressFill: {
+    height: '100%',
+    backgroundColor: Colors.primary[500],
+    borderRadius: 2,
+  },
+  
+  progressText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+    textAlign: 'center',
+  },
+  
+  header: {
+    alignItems: 'center',
+    marginBottom: Spacing.xl,
+  },
+  
+  title: {
+    ...Typography.styles.h2,
+    color: Colors.text.primary,
+    textAlign: 'center',
+    marginBottom: Spacing.sm,
+  },
+  
+  subtitle: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+    textAlign: 'center',
+    maxWidth: 300,
+  },
+  
+  contentCard: {
+    flex: 1,
+    marginBottom: Spacing.xl,
+  },
+  
+  navigation: {
+    flexDirection: 'row',
+    gap: Spacing.md,
+  },
+  
+  backButton: {
+    flex: 1,
+  },
+  
+  nextButton: {
+    flex: 2,
+  },
+});
diff --git a/components/ui/Button.tsx b/components/ui/Button.tsx
new file mode 100644
index 0000000..5dd789c
--- /dev/null
+++ b/components/ui/Button.tsx
@@ -0,0 +1,181 @@
+import React from 'react';
+import {
+  TouchableOpacity,
+  Text,
+  StyleSheet,
+  ViewStyle,
+  TextStyle,
+  ActivityIndicator,
+} from 'react-native';
+import { Colors } from '../../constants/Colors';
+import { Typography } from '../../constants/Typography';
+import { Spacing } from '../../constants/Spacing';
+
+export interface ButtonProps {
+  title: string;
+  onPress: () => void;
+  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
+  size?: 'small' | 'medium' | 'large';
+  disabled?: boolean;
+  loading?: boolean;
+  style?: ViewStyle;
+  textStyle?: TextStyle;
+  fullWidth?: boolean;
+}
+
+export const Button: React.FC<ButtonProps> = ({
+  title,
+  onPress,
+  variant = 'primary',
+  size = 'medium',
+  disabled = false,
+  loading = false,
+  style,
+  textStyle,
+  fullWidth = false,
+}) => {
+  const buttonStyle = [
+    styles.base,
+    styles[variant],
+    styles[size],
+    fullWidth && styles.fullWidth,
+    disabled && styles.disabled,
+    style,
+  ];
+
+  const textStyleCombined = [
+    styles.text,
+    styles[`${variant}Text`],
+    styles[`${size}Text`],
+    disabled && styles.disabledText,
+    textStyle,
+  ];
+
+  return (
+    <TouchableOpacity
+      style={buttonStyle}
+      onPress={onPress}
+      disabled={disabled || loading}
+      activeOpacity={0.8}
+    >
+      {loading ? (
+        <ActivityIndicator
+          color={variant === 'primary' ? Colors.text.inverse : Colors.primary[500]}
+          size="small"
+        />
+      ) : (
+        <Text style={textStyleCombined}>{title}</Text>
+      )}
+    </TouchableOpacity>
+  );
+};
+
+const styles = StyleSheet.create({
+  base: {
+    borderRadius: 12,
+    alignItems: 'center',
+    justifyContent: 'center',
+    flexDirection: 'row',
+    minHeight: Spacing.component.touchTarget,
+  },
+  
+  // Variants
+  primary: {
+    backgroundColor: Colors.primary[500],
+    shadowColor: Colors.primary[500],
+    shadowOffset: { width: 0, height: 2 },
+    shadowOpacity: 0.2,
+    shadowRadius: 4,
+    elevation: 4,
+  },
+  secondary: {
+    backgroundColor: Colors.secondary[500],
+    shadowColor: Colors.secondary[500],
+    shadowOffset: { width: 0, height: 2 },
+    shadowOpacity: 0.2,
+    shadowRadius: 4,
+    elevation: 4,
+  },
+  outline: {
+    backgroundColor: 'transparent',
+    borderWidth: 2,
+    borderColor: Colors.primary[500],
+  },
+  ghost: {
+    backgroundColor: 'transparent',
+  },
+  danger: {
+    backgroundColor: Colors.error,
+    shadowColor: Colors.error,
+    shadowOffset: { width: 0, height: 2 },
+    shadowOpacity: 0.2,
+    shadowRadius: 4,
+    elevation: 4,
+  },
+
+  // Sizes
+  small: {
+    paddingVertical: Spacing.component.buttonPaddingVerticalSmall,
+    paddingHorizontal: Spacing.component.buttonPaddingHorizontalSmall,
+    minHeight: 36,
+  },
+  medium: {
+    paddingVertical: Spacing.component.buttonPaddingVertical,
+    paddingHorizontal: Spacing.component.buttonPaddingHorizontal,
+  },
+  large: {
+    paddingVertical: Spacing.component.buttonPaddingVerticalLarge,
+    paddingHorizontal: Spacing.component.buttonPaddingHorizontalLarge,
+    minHeight: Spacing.component.touchTargetLarge,
+  },
+
+  // Text styles
+  text: {
+    textAlign: 'center',
+    fontWeight: '600',
+  },
+  primaryText: {
+    color: Colors.text.inverse,
+    ...Typography.styles.button,
+  },
+  secondaryText: {
+    color: Colors.text.inverse,
+    ...Typography.styles.button,
+  },
+  outlineText: {
+    color: Colors.primary[500],
+    ...Typography.styles.button,
+  },
+  ghostText: {
+    color: Colors.primary[500],
+    ...Typography.styles.button,
+  },
+  dangerText: {
+    color: Colors.text.inverse,
+    ...Typography.styles.button,
+  },
+
+  // Size-specific text
+  smallText: {
+    ...Typography.styles.buttonSmall,
+  },
+  mediumText: {
+    ...Typography.styles.button,
+  },
+  largeText: {
+    ...Typography.styles.buttonLarge,
+  },
+
+  // States
+  disabled: {
+    opacity: 0.5,
+    shadowOpacity: 0,
+    elevation: 0,
+  },
+  disabledText: {
+    opacity: 0.7,
+  },
+  fullWidth: {
+    width: '100%',
+  },
+});
diff --git a/components/ui/Card.tsx b/components/ui/Card.tsx
new file mode 100644
index 0000000..e118d51
--- /dev/null
+++ b/components/ui/Card.tsx
@@ -0,0 +1,66 @@
+import React from 'react';
+import { View, StyleSheet, ViewStyle } from 'react-native';
+import { Colors } from '../../constants/Colors';
+import { Spacing } from '../../constants/Spacing';
+
+export interface CardProps {
+  children: React.ReactNode;
+  style?: ViewStyle;
+  padding?: 'none' | 'small' | 'medium' | 'large';
+  shadow?: boolean;
+  border?: boolean;
+}
+
+export const Card: React.FC<CardProps> = ({
+  children,
+  style,
+  padding = 'medium',
+  shadow = true,
+  border = false,
+}) => {
+  const cardStyle = [
+    styles.base,
+    styles[padding],
+    shadow && styles.shadow,
+    border && styles.border,
+    style,
+  ];
+
+  return <View style={cardStyle}>{children}</View>;
+};
+
+const styles = StyleSheet.create({
+  base: {
+    backgroundColor: Colors.background.primary,
+    borderRadius: 16,
+  },
+  
+  // Padding variants
+  none: {
+    padding: 0,
+  },
+  small: {
+    padding: Spacing.sm,
+  },
+  medium: {
+    padding: Spacing.component.cardPadding,
+  },
+  large: {
+    padding: Spacing.component.cardPaddingLarge,
+  },
+  
+  // Shadow
+  shadow: {
+    shadowColor: Colors.text.primary,
+    shadowOffset: { width: 0, height: 2 },
+    shadowOpacity: 0.1,
+    shadowRadius: 8,
+    elevation: 4,
+  },
+  
+  // Border
+  border: {
+    borderWidth: 1,
+    borderColor: Colors.border.light,
+  },
+});
diff --git a/components/ui/Input.tsx b/components/ui/Input.tsx
new file mode 100644
index 0000000..c116893
--- /dev/null
+++ b/components/ui/Input.tsx
@@ -0,0 +1,163 @@
+import React, { useState } from 'react';
+import {
+  TextInput,
+  View,
+  Text,
+  StyleSheet,
+  ViewStyle,
+  TextStyle,
+  TextInputProps,
+} from 'react-native';
+import { Colors } from '../../constants/Colors';
+import { Typography } from '../../constants/Typography';
+import { Spacing } from '../../constants/Spacing';
+
+export interface InputProps extends Omit<TextInputProps, 'style'> {
+  label?: string;
+  error?: string;
+  hint?: string;
+  style?: ViewStyle;
+  inputStyle?: TextStyle;
+  required?: boolean;
+  leftIcon?: React.ReactNode;
+  rightIcon?: React.ReactNode;
+}
+
+export const Input: React.FC<InputProps> = ({
+  label,
+  error,
+  hint,
+  style,
+  inputStyle,
+  required = false,
+  leftIcon,
+  rightIcon,
+  ...textInputProps
+}) => {
+  const [isFocused, setIsFocused] = useState(false);
+
+  const containerStyle = [
+    styles.container,
+    style,
+  ];
+
+  const inputContainerStyle = [
+    styles.inputContainer,
+    isFocused && styles.inputContainerFocused,
+    error && styles.inputContainerError,
+  ];
+
+  const textInputStyle = [
+    styles.input,
+    leftIcon && styles.inputWithLeftIcon,
+    rightIcon && styles.inputWithRightIcon,
+    inputStyle,
+  ];
+
+  return (
+    <View style={containerStyle}>
+      {label && (
+        <Text style={styles.label}>
+          {label}
+          {required && <Text style={styles.required}> *</Text>}
+        </Text>
+      )}
+      
+      <View style={inputContainerStyle}>
+        {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
+        
+        <TextInput
+          style={textInputStyle}
+          onFocus={() => setIsFocused(true)}
+          onBlur={() => setIsFocused(false)}
+          placeholderTextColor={Colors.text.tertiary}
+          {...textInputProps}
+        />
+        
+        {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
+      </View>
+      
+      {error && <Text style={styles.error}>{error}</Text>}
+      {hint && !error && <Text style={styles.hint}>{hint}</Text>}
+    </View>
+  );
+};
+
+const styles = StyleSheet.create({
+  container: {
+    marginBottom: Spacing.md,
+  },
+  
+  label: {
+    ...Typography.styles.label,
+    color: Colors.text.primary,
+    marginBottom: Spacing.sm,
+  },
+  
+  required: {
+    color: Colors.error,
+  },
+  
+  inputContainer: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    borderWidth: 1,
+    borderColor: Colors.border.medium,
+    borderRadius: 12,
+    backgroundColor: Colors.background.primary,
+    minHeight: Spacing.component.touchTarget,
+  },
+  
+  inputContainerFocused: {
+    borderColor: Colors.primary[500],
+    shadowColor: Colors.primary[500],
+    shadowOffset: { width: 0, height: 0 },
+    shadowOpacity: 0.1,
+    shadowRadius: 4,
+    elevation: 2,
+  },
+  
+  inputContainerError: {
+    borderColor: Colors.error,
+  },
+  
+  input: {
+    flex: 1,
+    paddingVertical: Spacing.component.inputPaddingVertical,
+    paddingHorizontal: Spacing.component.inputPaddingHorizontal,
+    ...Typography.styles.input,
+    color: Colors.text.primary,
+  },
+  
+  inputWithLeftIcon: {
+    paddingLeft: Spacing.sm,
+  },
+  
+  inputWithRightIcon: {
+    paddingRight: Spacing.sm,
+  },
+  
+  leftIcon: {
+    paddingLeft: Spacing.component.inputPaddingHorizontal,
+    justifyContent: 'center',
+    alignItems: 'center',
+  },
+  
+  rightIcon: {
+    paddingRight: Spacing.component.inputPaddingHorizontal,
+    justifyContent: 'center',
+    alignItems: 'center',
+  },
+  
+  error: {
+    ...Typography.styles.caption,
+    color: Colors.error,
+    marginTop: Spacing.xs,
+  },
+  
+  hint: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+    marginTop: Spacing.xs,
+  },
+});
diff --git a/constants/Colors.ts b/constants/Colors.ts
new file mode 100644
index 0000000..9a9ea85
--- /dev/null
+++ b/constants/Colors.ts
@@ -0,0 +1,114 @@
+/**
+ * Color system designed for conflict resolution scenarios
+ * Focuses on calming, trustworthy colors that promote emotional safety
+ */
+
+export const Colors = {
+  // Primary brand colors - calming and trustworthy
+  primary: {
+    50: '#f0f9ff',
+    100: '#e0f2fe',
+    200: '#bae6fd',
+    300: '#7dd3fc',
+    400: '#38bdf8',
+    500: '#0ea5e9', // Main primary
+    600: '#0284c7',
+    700: '#0369a1',
+    800: '#075985',
+    900: '#0c4a6e',
+  },
+
+  // Secondary colors - warm and supportive
+  secondary: {
+    50: '#fdf4ff',
+    100: '#fae8ff',
+    200: '#f5d0fe',
+    300: '#f0abfc',
+    400: '#e879f9',
+    500: '#d946ef',
+    600: '#c026d3',
+    700: '#a21caf',
+    800: '#86198f',
+    900: '#701a75',
+  },
+
+  // Emotion-based colors for conflict resolution
+  emotion: {
+    // Positive emotions - greens
+    positive: '#10b981',
+    calm: '#059669',
+    peaceful: '#047857',
+    
+    // Neutral emotions - blues/grays
+    neutral: '#6b7280',
+    balanced: '#4b5563',
+    centered: '#374151',
+    
+    // Challenging emotions - warm oranges (not aggressive reds)
+    tension: '#f59e0b',
+    conflict: '#d97706',
+    intense: '#b45309',
+    
+    // Alert/warning - soft reds
+    alert: '#ef4444',
+    warning: '#dc2626',
+    critical: '#b91c1c',
+  },
+
+  // Conflict level indicators
+  conflict: {
+    low: '#10b981',      // Green - peaceful
+    moderate: '#f59e0b',  // Amber - some tension
+    high: '#ef4444',     // Red - needs attention
+    resolved: '#8b5cf6', // Purple - resolution achieved
+  },
+
+  // UI colors
+  background: {
+    primary: '#ffffff',
+    secondary: '#f8fafc',
+    tertiary: '#f1f5f9',
+    dark: '#0f172a',
+    overlay: 'rgba(0, 0, 0, 0.5)',
+  },
+
+  text: {
+    primary: '#1e293b',
+    secondary: '#475569',
+    tertiary: '#64748b',
+    inverse: '#ffffff',
+    muted: '#94a3b8',
+  },
+
+  border: {
+    light: '#e2e8f0',
+    medium: '#cbd5e1',
+    dark: '#94a3b8',
+  },
+
+  // Voice interaction states
+  voice: {
+    listening: '#10b981',
+    speaking: '#3b82f6',
+    processing: '#f59e0b',
+    inactive: '#6b7280',
+    error: '#ef4444',
+  },
+
+  // Success/error states
+  success: '#10b981',
+  error: '#ef4444',
+  warning: '#f59e0b',
+  info: '#3b82f6',
+
+  // Gradients for backgrounds
+  gradients: {
+    primary: ['#4ECDC4', '#44A08D', '#45B7D1'],
+    calm: ['#a8edea', '#fed6e3'],
+    peaceful: ['#d299c2', '#fef9d7'],
+    supportive: ['#89f7fe', '#66a6ff'],
+  },
+} as const;
+
+export type ColorKey = keyof typeof Colors;
+export type ColorValue = typeof Colors[ColorKey];
diff --git a/constants/Spacing.ts b/constants/Spacing.ts
new file mode 100644
index 0000000..151b64a
--- /dev/null
+++ b/constants/Spacing.ts
@@ -0,0 +1,84 @@
+/**
+ * Spacing system for consistent layout and comfortable touch targets
+ * Optimized for both mobile and web interfaces
+ */
+
+export const Spacing = {
+  // Base spacing units
+  xs: 4,
+  sm: 8,
+  md: 16,
+  lg: 24,
+  xl: 32,
+  '2xl': 48,
+  '3xl': 64,
+  '4xl': 96,
+  '5xl': 128,
+
+  // Component-specific spacing
+  component: {
+    // Touch targets - minimum 44px for accessibility
+    touchTarget: 44,
+    touchTargetLarge: 56,
+    
+    // Buttons
+    buttonPaddingVertical: 12,
+    buttonPaddingHorizontal: 24,
+    buttonPaddingVerticalLarge: 16,
+    buttonPaddingHorizontalLarge: 32,
+    buttonPaddingVerticalSmall: 8,
+    buttonPaddingHorizontalSmall: 16,
+    
+    // Input fields
+    inputPaddingVertical: 12,
+    inputPaddingHorizontal: 16,
+    
+    // Cards and containers
+    cardPadding: 16,
+    cardPaddingLarge: 24,
+    cardMargin: 16,
+    
+    // Screen margins
+    screenPadding: 20,
+    screenPaddingLarge: 32,
+    
+    // Voice interface - larger spacing for emotional comfort
+    voiceButtonSize: 120,
+    voiceButtonMargin: 32,
+    
+    // Session interface spacing
+    sessionPadding: 24,
+    emotionIndicatorSpacing: 12,
+    conflictMeterSize: 80,
+  },
+
+  // Layout spacing
+  layout: {
+    // Section spacing
+    sectionSpacing: 32,
+    sectionSpacingLarge: 48,
+    
+    // Content spacing
+    contentSpacing: 16,
+    contentSpacingLarge: 24,
+    
+    // List item spacing
+    listItemSpacing: 12,
+    listItemSpacingLarge: 16,
+    
+    // Navigation
+    navItemSpacing: 8,
+    navSectionSpacing: 24,
+  },
+
+  // Responsive breakpoints
+  breakpoints: {
+    mobile: 0,
+    tablet: 768,
+    desktop: 1024,
+    largeDesktop: 1440,
+  },
+} as const;
+
+export type SpacingKey = keyof typeof Spacing;
+export type SpacingValue = typeof Spacing[SpacingKey];
diff --git a/constants/Typography.ts b/constants/Typography.ts
new file mode 100644
index 0000000..e0b3c71
--- /dev/null
+++ b/constants/Typography.ts
@@ -0,0 +1,196 @@
+/**
+ * Typography system optimized for conflict resolution scenarios
+ * Focuses on readability during emotional situations
+ */
+
+export const Typography = {
+  // Font families
+  fonts: {
+    primary: 'Inter-Regular',
+    primaryBold: 'Inter-Bold',
+    primarySemiBold: 'Inter-SemiBold',
+    primaryMedium: 'Inter-Medium',
+    primaryLight: 'Inter-Light',
+  },
+
+  // Font sizes - optimized for emotional readability
+  sizes: {
+    xs: 12,
+    sm: 14,
+    base: 16,
+    lg: 18,
+    xl: 20,
+    '2xl': 24,
+    '3xl': 30,
+    '4xl': 36,
+    '5xl': 48,
+    '6xl': 60,
+  },
+
+  // Line heights for better readability
+  lineHeights: {
+    tight: 1.25,
+    normal: 1.5,
+    relaxed: 1.75,
+    loose: 2,
+  },
+
+  // Letter spacing
+  letterSpacing: {
+    tight: -0.025,
+    normal: 0,
+    wide: 0.025,
+    wider: 0.05,
+  },
+
+  // Text styles for specific use cases
+  styles: {
+    // Headers
+    h1: {
+      fontSize: 36,
+      fontFamily: 'Inter-Bold',
+      lineHeight: 1.25,
+      letterSpacing: -0.025,
+    },
+    h2: {
+      fontSize: 30,
+      fontFamily: 'Inter-Bold',
+      lineHeight: 1.25,
+      letterSpacing: -0.025,
+    },
+    h3: {
+      fontSize: 24,
+      fontFamily: 'Inter-SemiBold',
+      lineHeight: 1.25,
+    },
+    h4: {
+      fontSize: 20,
+      fontFamily: 'Inter-SemiBold',
+      lineHeight: 1.25,
+    },
+    h5: {
+      fontSize: 18,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+    },
+    h6: {
+      fontSize: 16,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+    },
+
+    // Body text - optimized for emotional readability
+    body: {
+      fontSize: 16,
+      fontFamily: 'Inter-Regular',
+      lineHeight: 1.5,
+    },
+    bodyLarge: {
+      fontSize: 18,
+      fontFamily: 'Inter-Regular',
+      lineHeight: 1.5,
+    },
+    bodySmall: {
+      fontSize: 14,
+      fontFamily: 'Inter-Regular',
+      lineHeight: 1.5,
+    },
+
+    // UI text
+    button: {
+      fontSize: 16,
+      fontFamily: 'Inter-SemiBold',
+      lineHeight: 1.25,
+      letterSpacing: 0.025,
+    },
+    buttonLarge: {
+      fontSize: 18,
+      fontFamily: 'Inter-SemiBold',
+      lineHeight: 1.25,
+      letterSpacing: 0.025,
+    },
+    buttonSmall: {
+      fontSize: 14,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+      letterSpacing: 0.025,
+    },
+
+    // Form elements
+    input: {
+      fontSize: 16,
+      fontFamily: 'Inter-Regular',
+      lineHeight: 1.25,
+    },
+    label: {
+      fontSize: 14,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+    },
+    placeholder: {
+      fontSize: 16,
+      fontFamily: 'Inter-Regular',
+      lineHeight: 1.25,
+    },
+
+    // Navigation
+    navItem: {
+      fontSize: 16,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+    },
+    navTitle: {
+      fontSize: 18,
+      fontFamily: 'Inter-SemiBold',
+      lineHeight: 1.25,
+    },
+
+    // Session-specific text
+    transcription: {
+      fontSize: 16,
+      fontFamily: 'Inter-Regular',
+      lineHeight: 1.75, // Extra spacing for readability during emotional moments
+    },
+    emotionLabel: {
+      fontSize: 12,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+      letterSpacing: 0.025,
+    },
+    conflictLevel: {
+      fontSize: 14,
+      fontFamily: 'Inter-SemiBold',
+      lineHeight: 1.25,
+    },
+
+    // Analytics and data
+    metric: {
+      fontSize: 24,
+      fontFamily: 'Inter-Bold',
+      lineHeight: 1.25,
+    },
+    metricLabel: {
+      fontSize: 12,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+      letterSpacing: 0.025,
+    },
+
+    // Captions and small text
+    caption: {
+      fontSize: 12,
+      fontFamily: 'Inter-Regular',
+      lineHeight: 1.25,
+    },
+    overline: {
+      fontSize: 10,
+      fontFamily: 'Inter-Medium',
+      lineHeight: 1.25,
+      letterSpacing: 0.05,
+      textTransform: 'uppercase' as const,
+    },
+  },
+} as const;
+
+export type TypographyStyle = keyof typeof Typography.styles;
+export type FontSize = keyof typeof Typography.sizes;
diff --git a/package.json b/package.json
index 7902f5d..8150e82 100644
--- a/package.json
+++ b/package.json
@@ -26,8 +26,13 @@
 		"expo-av": "~15.1.6",
 		"expo-battery": "~9.1.4",
 		"expo-brightness": "~13.1.4",
+		"expo-camera": "~16.1.5",
 		"expo-dev-client": "~5.2.2",
+		"expo-document-picker": "~12.1.2",
+		"expo-file-system": "~18.1.5",
 		"expo-font": "~13.3.1",
+		"expo-image-picker": "~16.1.5",
+		"expo-media-library": "~17.1.5",
 		"expo-linear-gradient": "~14.1.5",
 		"expo-permissions": "^14.4.0",
 		"expo-router": "~5.1.1",
@@ -41,6 +46,7 @@
 		"react-native-reanimated": "~3.16.7",
 		"react-native-safe-area-context": "^5.5.0",
 		"react-native-screens": "^4.11.1",
+		"react-native-svg": "15.8.0",
 		"react-native-web": "~0.20.0",
 		"react-native-webview": "13.15.0",
 		"zustand": "^5.0.6"
diff --git a/services/ai/orchestrationEngine.ts b/services/ai/orchestrationEngine.ts
index b402402..17992a0 100644
--- a/services/ai/orchestrationEngine.ts
+++ b/services/ai/orchestrationEngine.ts
@@ -12,8 +12,8 @@ import { elevenLabsConversationalAI, elevenLabsTTS } from '../elevenlabs/convers
 
 // Gemini Models Configuration (ONLY for document analysis)
 export const GEMINI_MODELS = {
-  DOCUMENT_ANALYSIS: google('gemini-1.5-pro'), // ONLY for document analysis
-  CONFLICT_ANALYSIS: google('gemini-1.5-flash'), // For conflict pattern analysis
+  DOCUMENT_ANALYSIS: google('gemini-2.0-flash-exp'), // ONLY for document analysis
+  CONFLICT_ANALYSIS: google('gemini-2.0-flash-exp'), // For conflict pattern analysis
 };
 
 // Orchestration Configuration
diff --git a/stores/authStore.ts b/stores/authStore.ts
index 5d51f48..12563eb 100644
--- a/stores/authStore.ts
+++ b/stores/authStore.ts
@@ -5,15 +5,29 @@ interface User {
   id: string;
   email: string;
   name: string;
+  username?: string;
   bio?: string;
   conflictStyle?: string;
   goals?: string;
+  location?: string;
+  personalityProfile?: Record<string, any>;
+  onboardingComplete?: boolean;
+}
+
+export interface OnboardingData {
+  username?: string;
+  password?: string;
+  confirmPassword?: string;
+  location?: string;
+  personalityAnswers?: Record<string, any>;
+  currentStep?: number;
 }
 
 interface AuthState {
   user: User | null;
   isLoading: boolean;
   isAuthenticated: boolean;
+  onboardingData: OnboardingData;
   
   // Actions
   login: (email: string, password: string) => Promise<void>;
@@ -21,12 +35,18 @@ interface AuthState {
   logout: () => void;
   updateProfile: (profile: Partial<User>) => Promise<void>;
   loadStoredAuth: () => Promise<void>;
+  updateOnboardingData: (data: Partial<OnboardingData>) => void;
+  completeOnboarding: () => Promise<void>;
+  resetOnboarding: () => void;
 }
 
 export const useAuthStore = create<AuthState>((set, get) => ({
   user: null,
   isLoading: false,
   isAuthenticated: false,
+  onboardingData: {
+    currentStep: 0,
+  },
   
   login: async (email: string, password: string) => {
     set({ isLoading: true });
@@ -93,4 +113,28 @@ export const useAuthStore = create<AuthState>((set, get) => ({
       console.error('Failed to load stored auth:', error);
     }
   },
+  
+  updateOnboardingData: (data) => set((state) => ({
+    onboardingData: { ...state.onboardingData, ...data }
+  })),
+  
+  completeOnboarding: async () => {
+    const { user, onboardingData } = get();
+    if (!user) return;
+    
+    const updatedUser = {
+      ...user,
+      username: onboardingData.username,
+      location: onboardingData.location,
+      personalityProfile: onboardingData.personalityAnswers,
+      onboardingComplete: true,
+    };
+    
+    await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
+    set({ user: updatedUser });
+  },
+  
+  resetOnboarding: () => set({
+    onboardingData: { currentStep: 0 },
+  }),
 }));
-- 
2.39.5


From 8cf3252bcb289854d86c362e2f51f4de6d9b8891 Mon Sep 17 00:00:00 2001
From: "codegen-sh[bot]" <131295404+codegen-sh[bot]@users.noreply.github.com>
Date: Mon, 30 Jun 2025 13:09:51 +0000
Subject: [PATCH 2/2] feat: implement core session functionality and dashboard

- Add comprehensive dashboard with stats, recent sessions, and quick actions
- Create session description screen with conflict input and session type selection
- Implement live session interface with real-time voice controls
- Add VoiceButton component with listening/processing states
- Create emotion visualization and conflict level meters
- Add session timer and AI insights display
- Implement username onboarding screen with validation
- Add responsive design patterns throughout
---
 app/(auth)/onboarding/username.tsx | 174 +++++++++++
 app/(main)/dashboard.tsx           | 441 +++++++++++++++++++++++++++
 app/(main)/session/describe.tsx    | 356 ++++++++++++++++++++++
 app/(main)/session/live.tsx        | 461 +++++++++++++++++++++++++++++
 components/session/VoiceButton.tsx | 130 ++++++++
 5 files changed, 1562 insertions(+)
 create mode 100644 app/(auth)/onboarding/username.tsx
 create mode 100644 app/(main)/dashboard.tsx
 create mode 100644 app/(main)/session/describe.tsx
 create mode 100644 app/(main)/session/live.tsx
 create mode 100644 components/session/VoiceButton.tsx

diff --git a/app/(auth)/onboarding/username.tsx b/app/(auth)/onboarding/username.tsx
new file mode 100644
index 0000000..f1bfd3a
--- /dev/null
+++ b/app/(auth)/onboarding/username.tsx
@@ -0,0 +1,174 @@
+import React, { useState } from 'react';
+import { View, Text, StyleSheet } from 'react-native';
+import { router } from 'expo-router';
+import { User } from 'lucide-react-native';
+
+import { OnboardingStep } from '../../../components/onboarding/OnboardingStep';
+import { Input } from '../../../components/ui/Input';
+import { useAuthStore } from '../../../stores/authStore';
+import { Colors } from '../../../constants/Colors';
+import { Typography } from '../../../constants/Typography';
+import { Spacing } from '../../../constants/Spacing';
+
+export default function UsernameScreen() {
+  const [username, setUsername] = useState('');
+  const [error, setError] = useState('');
+  
+  const { updateOnboardingData, onboardingData } = useAuthStore();
+
+  const validateUsername = (username: string) => {
+    if (!username.trim()) {
+      setError('Username is required');
+      return false;
+    }
+    if (username.trim().length < 3) {
+      setError('Username must be at least 3 characters');
+      return false;
+    }
+    if (username.trim().length > 20) {
+      setError('Username must be less than 20 characters');
+      return false;
+    }
+    if (!/^[a-zA-Z0-9_]+$/.test(username.trim())) {
+      setError('Username can only contain letters, numbers, and underscores');
+      return false;
+    }
+    setError('');
+    return true;
+  };
+
+  const handleNext = () => {
+    if (validateUsername(username)) {
+      updateOnboardingData({ username: username.trim(), currentStep: 2 });
+      router.push('/(auth)/onboarding/password');
+    }
+  };
+
+  const handleBack = () => {
+    router.back();
+  };
+
+  const handleUsernameChange = (text: string) => {
+    setUsername(text);
+    if (error) {
+      validateUsername(text);
+    }
+  };
+
+  return (
+    <OnboardingStep
+      title="Choose Your Username"
+      subtitle="This will be how others see you in shared sessions"
+      currentStep={2}
+      totalSteps={7}
+      onNext={handleNext}
+      onBack={handleBack}
+      nextDisabled={!username.trim() || !!error}
+    >
+      <View style={styles.content}>
+        <View style={styles.inputSection}>
+          <Input
+            label="Username"
+            value={username}
+            onChangeText={handleUsernameChange}
+            placeholder="Enter your username"
+            autoCapitalize="none"
+            autoComplete="username"
+            error={error}
+            leftIcon={<User size={20} color={Colors.text.tertiary} />}
+            style={styles.input}
+          />
+          
+          <View style={styles.tips}>
+            <Text style={styles.tipsTitle}>Tips for a good username:</Text>
+            <Text style={styles.tip}>• Use 3-20 characters</Text>
+            <Text style={styles.tip}>• Letters, numbers, and underscores only</Text>
+            <Text style={styles.tip}>• Choose something you're comfortable sharing</Text>
+          </View>
+        </View>
+
+        <View style={styles.preview}>
+          <Text style={styles.previewLabel}>Preview:</Text>
+          <View style={styles.previewCard}>
+            <View style={styles.avatar}>
+              <User size={24} color={Colors.text.inverse} />
+            </View>
+            <Text style={styles.previewUsername}>
+              {username.trim() || 'Your Username'}
+            </Text>
+          </View>
+        </View>
+      </View>
+    </OnboardingStep>
+  );
+}
+
+const styles = StyleSheet.create({
+  content: {
+    flex: 1,
+    justifyContent: 'space-between',
+  },
+  
+  inputSection: {
+    gap: Spacing.xl,
+  },
+  
+  input: {
+    marginBottom: 0,
+  },
+  
+  tips: {
+    backgroundColor: Colors.background.tertiary,
+    padding: Spacing.md,
+    borderRadius: 12,
+  },
+  
+  tipsTitle: {
+    ...Typography.styles.label,
+    color: Colors.text.primary,
+    marginBottom: Spacing.sm,
+  },
+  
+  tip: {
+    ...Typography.styles.caption,
+    color: Colors.text.secondary,
+    marginBottom: Spacing.xs,
+  },
+  
+  preview: {
+    alignItems: 'center',
+  },
+  
+  previewLabel: {
+    ...Typography.styles.label,
+    color: Colors.text.secondary,
+    marginBottom: Spacing.sm,
+  },
+  
+  previewCard: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    backgroundColor: Colors.primary[50],
+    paddingHorizontal: Spacing.md,
+    paddingVertical: Spacing.sm,
+    borderRadius: 20,
+    borderWidth: 1,
+    borderColor: Colors.primary[200],
+  },
+  
+  avatar: {
+    width: 32,
+    height: 32,
+    borderRadius: 16,
+    backgroundColor: Colors.primary[500],
+    justifyContent: 'center',
+    alignItems: 'center',
+    marginRight: Spacing.sm,
+  },
+  
+  previewUsername: {
+    ...Typography.styles.body,
+    color: Colors.primary[700],
+    fontWeight: '600',
+  },
+});
diff --git a/app/(main)/dashboard.tsx b/app/(main)/dashboard.tsx
new file mode 100644
index 0000000..ebcf8ac
--- /dev/null
+++ b/app/(main)/dashboard.tsx
@@ -0,0 +1,441 @@
+import React from 'react';
+import {
+  View,
+  Text,
+  StyleSheet,
+  ScrollView,
+  TouchableOpacity,
+  Dimensions,
+} from 'react-native';
+import { router } from 'expo-router';
+import { 
+  Plus, 
+  MessageCircle, 
+  TrendingUp, 
+  Clock, 
+  Users, 
+  Award,
+  Mic,
+  BarChart3
+} from 'lucide-react-native';
+
+import { ResponsiveLayout } from '../../components/layout/ResponsiveLayout';
+import { Card } from '../../components/ui/Card';
+import { Button } from '../../components/ui/Button';
+import { useAuthStore } from '../../stores/authStore';
+import { Colors } from '../../constants/Colors';
+import { Typography } from '../../constants/Typography';
+import { Spacing } from '../../constants/Spacing';
+
+const { width: screenWidth } = Dimensions.get('window');
+
+export default function DashboardScreen() {
+  const { user } = useAuthStore();
+  
+  const isTablet = screenWidth >= 768;
+
+  const handleStartSession = () => {
+    router.push('/(main)/session/describe');
+  };
+
+  const handleViewHistory = () => {
+    router.push('/(main)/growth/history');
+  };
+
+  const handleViewGrowth = () => {
+    router.push('/(main)/growth');
+  };
+
+  const handleViewSettings = () => {
+    router.push('/(main)/settings');
+  };
+
+  // Mock data for demonstration
+  const recentSessions = [
+    {
+      id: '1',
+      title: 'Family Discussion',
+      date: '2 hours ago',
+      participants: 3,
+      resolution: 'positive',
+    },
+    {
+      id: '2',
+      title: 'Work Conflict',
+      date: 'Yesterday',
+      participants: 2,
+      resolution: 'moderate',
+    },
+  ];
+
+  const stats = {
+    totalSessions: 12,
+    avgResolution: 85,
+    growthScore: 7.8,
+    streakDays: 5,
+  };
+
+  return (
+    <ResponsiveLayout>
+      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
+        {/* Header */}
+        <View style={styles.header}>
+          <View>
+            <Text style={styles.greeting}>
+              Welcome back, {user?.name || user?.username || 'User'}!
+            </Text>
+            <Text style={styles.subtitle}>
+              Ready to improve your communication skills?
+            </Text>
+          </View>
+          <TouchableOpacity onPress={handleViewSettings} style={styles.profileButton}>
+            <View style={styles.avatar}>
+              <Text style={styles.avatarText}>
+                {(user?.name || user?.username || 'U')[0].toUpperCase()}
+              </Text>
+            </View>
+          </TouchableOpacity>
+        </View>
+
+        {/* Quick Start Section */}
+        <Card style={styles.quickStartCard}>
+          <View style={styles.quickStartContent}>
+            <View style={styles.quickStartText}>
+              <Text style={styles.quickStartTitle}>Start a New Session</Text>
+              <Text style={styles.quickStartDescription}>
+                Begin a conflict resolution session with AI guidance
+              </Text>
+            </View>
+            <Button
+              title="Start Session"
+              onPress={handleStartSession}
+              size="large"
+              style={styles.startButton}
+            />
+          </View>
+          <View style={styles.voiceHint}>
+            <Mic size={16} color={Colors.text.tertiary} />
+            <Text style={styles.voiceHintText}>Voice-guided experience available</Text>
+          </View>
+        </Card>
+
+        {/* Stats Overview */}
+        <View style={[styles.statsGrid, isTablet && styles.statsGridTablet]}>
+          <Card style={styles.statCard}>
+            <MessageCircle size={24} color={Colors.primary[500]} />
+            <Text style={styles.statNumber}>{stats.totalSessions}</Text>
+            <Text style={styles.statLabel}>Sessions</Text>
+          </Card>
+          
+          <Card style={styles.statCard}>
+            <TrendingUp size={24} color={Colors.emotion.positive} />
+            <Text style={styles.statNumber}>{stats.avgResolution}%</Text>
+            <Text style={styles.statLabel}>Avg Resolution</Text>
+          </Card>
+          
+          <Card style={styles.statCard}>
+            <Award size={24} color={Colors.secondary[500]} />
+            <Text style={styles.statNumber}>{stats.growthScore}</Text>
+            <Text style={styles.statLabel}>Growth Score</Text>
+          </Card>
+          
+          <Card style={styles.statCard}>
+            <Clock size={24} color={Colors.emotion.calm} />
+            <Text style={styles.statNumber}>{stats.streakDays}</Text>
+            <Text style={styles.statLabel}>Day Streak</Text>
+          </Card>
+        </View>
+
+        {/* Recent Sessions */}
+        <View style={styles.section}>
+          <View style={styles.sectionHeader}>
+            <Text style={styles.sectionTitle}>Recent Sessions</Text>
+            <TouchableOpacity onPress={handleViewHistory}>
+              <Text style={styles.sectionLink}>View All</Text>
+            </TouchableOpacity>
+          </View>
+          
+          {recentSessions.map((session) => (
+            <Card key={session.id} style={styles.sessionCard}>
+              <View style={styles.sessionHeader}>
+                <Text style={styles.sessionTitle}>{session.title}</Text>
+                <View style={[
+                  styles.resolutionBadge,
+                  { backgroundColor: getResolutionColor(session.resolution) }
+                ]}>
+                  <Text style={styles.resolutionText}>
+                    {session.resolution}
+                  </Text>
+                </View>
+              </View>
+              <View style={styles.sessionMeta}>
+                <View style={styles.sessionMetaItem}>
+                  <Clock size={14} color={Colors.text.tertiary} />
+                  <Text style={styles.sessionMetaText}>{session.date}</Text>
+                </View>
+                <View style={styles.sessionMetaItem}>
+                  <Users size={14} color={Colors.text.tertiary} />
+                  <Text style={styles.sessionMetaText}>
+                    {session.participants} participants
+                  </Text>
+                </View>
+              </View>
+            </Card>
+          ))}
+        </View>
+
+        {/* Quick Actions */}
+        <View style={styles.section}>
+          <Text style={styles.sectionTitle}>Quick Actions</Text>
+          <View style={styles.quickActions}>
+            <TouchableOpacity 
+              style={styles.quickAction}
+              onPress={handleViewGrowth}
+            >
+              <BarChart3 size={24} color={Colors.primary[500]} />
+              <Text style={styles.quickActionText}>View Growth</Text>
+            </TouchableOpacity>
+            
+            <TouchableOpacity 
+              style={styles.quickAction}
+              onPress={handleViewHistory}
+            >
+              <Clock size={24} color={Colors.secondary[500]} />
+              <Text style={styles.quickActionText}>Session History</Text>
+            </TouchableOpacity>
+            
+            <TouchableOpacity 
+              style={styles.quickAction}
+              onPress={handleViewSettings}
+            >
+              <Award size={24} color={Colors.emotion.positive} />
+              <Text style={styles.quickActionText}>Achievements</Text>
+            </TouchableOpacity>
+          </View>
+        </View>
+      </ScrollView>
+    </ResponsiveLayout>
+  );
+}
+
+function getResolutionColor(resolution: string) {
+  switch (resolution) {
+    case 'positive':
+      return Colors.emotion.positive;
+    case 'moderate':
+      return Colors.emotion.tension;
+    case 'challenging':
+      return Colors.emotion.conflict;
+    default:
+      return Colors.emotion.neutral;
+  }
+}
+
+const styles = StyleSheet.create({
+  container: {
+    flex: 1,
+  },
+  
+  header: {
+    flexDirection: 'row',
+    justifyContent: 'space-between',
+    alignItems: 'flex-start',
+    marginBottom: Spacing.xl,
+    paddingTop: Spacing.md,
+  },
+  
+  greeting: {
+    ...Typography.styles.h3,
+    color: Colors.text.primary,
+    marginBottom: Spacing.xs,
+  },
+  
+  subtitle: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+  },
+  
+  profileButton: {
+    padding: Spacing.xs,
+  },
+  
+  avatar: {
+    width: 48,
+    height: 48,
+    borderRadius: 24,
+    backgroundColor: Colors.primary[500],
+    justifyContent: 'center',
+    alignItems: 'center',
+  },
+  
+  avatarText: {
+    ...Typography.styles.h5,
+    color: Colors.text.inverse,
+    fontWeight: '600',
+  },
+  
+  quickStartCard: {
+    marginBottom: Spacing.xl,
+    backgroundColor: Colors.primary[50],
+    borderWidth: 1,
+    borderColor: Colors.primary[200],
+  },
+  
+  quickStartContent: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    justifyContent: 'space-between',
+    marginBottom: Spacing.md,
+  },
+  
+  quickStartText: {
+    flex: 1,
+    marginRight: Spacing.md,
+  },
+  
+  quickStartTitle: {
+    ...Typography.styles.h5,
+    color: Colors.text.primary,
+    marginBottom: Spacing.xs,
+  },
+  
+  quickStartDescription: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+  },
+  
+  startButton: {
+    minWidth: 120,
+  },
+  
+  voiceHint: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: Spacing.xs,
+  },
+  
+  voiceHintText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+  },
+  
+  statsGrid: {
+    flexDirection: 'row',
+    flexWrap: 'wrap',
+    gap: Spacing.md,
+    marginBottom: Spacing.xl,
+  },
+  
+  statsGridTablet: {
+    justifyContent: 'space-between',
+  },
+  
+  statCard: {
+    flex: 1,
+    minWidth: '45%',
+    alignItems: 'center',
+    padding: Spacing.md,
+  },
+  
+  statNumber: {
+    ...Typography.styles.metric,
+    color: Colors.text.primary,
+    marginTop: Spacing.sm,
+    marginBottom: Spacing.xs,
+  },
+  
+  statLabel: {
+    ...Typography.styles.metricLabel,
+    color: Colors.text.secondary,
+  },
+  
+  section: {
+    marginBottom: Spacing.xl,
+  },
+  
+  sectionHeader: {
+    flexDirection: 'row',
+    justifyContent: 'space-between',
+    alignItems: 'center',
+    marginBottom: Spacing.md,
+  },
+  
+  sectionTitle: {
+    ...Typography.styles.h5,
+    color: Colors.text.primary,
+  },
+  
+  sectionLink: {
+    ...Typography.styles.body,
+    color: Colors.primary[500],
+    fontWeight: '600',
+  },
+  
+  sessionCard: {
+    marginBottom: Spacing.md,
+  },
+  
+  sessionHeader: {
+    flexDirection: 'row',
+    justifyContent: 'space-between',
+    alignItems: 'center',
+    marginBottom: Spacing.sm,
+  },
+  
+  sessionTitle: {
+    ...Typography.styles.body,
+    color: Colors.text.primary,
+    fontWeight: '600',
+    flex: 1,
+  },
+  
+  resolutionBadge: {
+    paddingHorizontal: Spacing.sm,
+    paddingVertical: Spacing.xs,
+    borderRadius: 12,
+  },
+  
+  resolutionText: {
+    ...Typography.styles.caption,
+    color: Colors.text.inverse,
+    fontWeight: '600',
+    textTransform: 'capitalize',
+  },
+  
+  sessionMeta: {
+    flexDirection: 'row',
+    gap: Spacing.md,
+  },
+  
+  sessionMetaItem: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: Spacing.xs,
+  },
+  
+  sessionMetaText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+  },
+  
+  quickActions: {
+    flexDirection: 'row',
+    justifyContent: 'space-around',
+    gap: Spacing.md,
+  },
+  
+  quickAction: {
+    flex: 1,
+    alignItems: 'center',
+    padding: Spacing.md,
+    backgroundColor: Colors.background.tertiary,
+    borderRadius: 12,
+  },
+  
+  quickActionText: {
+    ...Typography.styles.caption,
+    color: Colors.text.secondary,
+    marginTop: Spacing.sm,
+    textAlign: 'center',
+  },
+});
diff --git a/app/(main)/session/describe.tsx b/app/(main)/session/describe.tsx
new file mode 100644
index 0000000..8ae6790
--- /dev/null
+++ b/app/(main)/session/describe.tsx
@@ -0,0 +1,356 @@
+import React, { useState } from 'react';
+import {
+  View,
+  Text,
+  StyleSheet,
+  ScrollView,
+  TouchableOpacity,
+  Alert,
+} from 'react-native';
+import { router } from 'expo-router';
+import { MessageCircle, Users, Clock, ArrowRight } from 'lucide-react-native';
+
+import { ResponsiveLayout } from '../../../components/layout/ResponsiveLayout';
+import { Card } from '../../../components/ui/Card';
+import { Button } from '../../../components/ui/Button';
+import { Input } from '../../../components/ui/Input';
+import { Colors } from '../../../constants/Colors';
+import { Typography } from '../../../constants/Typography';
+import { Spacing } from '../../../constants/Spacing';
+
+export default function DescribeConflictScreen() {
+  const [title, setTitle] = useState('');
+  const [description, setDescription] = useState('');
+  const [participants, setParticipants] = useState('2');
+  const [sessionType, setSessionType] = useState<'solo' | 'guided' | 'group'>('solo');
+  
+  const [titleError, setTitleError] = useState('');
+  const [descriptionError, setDescriptionError] = useState('');
+
+  const validateForm = () => {
+    let hasErrors = false;
+    
+    if (!title.trim()) {
+      setTitleError('Please provide a title for this session');
+      hasErrors = true;
+    } else {
+      setTitleError('');
+    }
+    
+    if (!description.trim()) {
+      setDescriptionError('Please describe the situation you\'d like to work on');
+      hasErrors = true;
+    } else if (description.trim().length < 20) {
+      setDescriptionError('Please provide more details (at least 20 characters)');
+      hasErrors = true;
+    } else {
+      setDescriptionError('');
+    }
+    
+    return !hasErrors;
+  };
+
+  const handleStartSession = () => {
+    if (validateForm()) {
+      // TODO: Save session data to store
+      router.push('/(main)/session/live');
+    }
+  };
+
+  const sessionTypes = [
+    {
+      id: 'solo',
+      title: 'Solo Practice',
+      description: 'Work through the conflict with AI guidance',
+      icon: MessageCircle,
+      color: Colors.primary[500],
+    },
+    {
+      id: 'guided',
+      title: 'Guided Session',
+      description: 'AI facilitates a conversation with others',
+      icon: Users,
+      color: Colors.secondary[500],
+    },
+    {
+      id: 'group',
+      title: 'Group Session',
+      description: 'Multiple participants with AI moderation',
+      icon: Clock,
+      color: Colors.emotion.positive,
+    },
+  ];
+
+  return (
+    <ResponsiveLayout>
+      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
+        <View style={styles.header}>
+          <Text style={styles.title}>Describe Your Situation</Text>
+          <Text style={styles.subtitle}>
+            Help us understand the conflict so we can provide the best guidance
+          </Text>
+        </View>
+
+        <Card style={styles.formCard}>
+          <View style={styles.form}>
+            <Input
+              label="Session Title"
+              value={title}
+              onChangeText={setTitle}
+              placeholder="e.g., Family dinner disagreement"
+              error={titleError}
+              required
+            />
+
+            <View style={styles.inputGroup}>
+              <Text style={styles.label}>
+                Describe the situation <Text style={styles.required}>*</Text>
+              </Text>
+              <Input
+                value={description}
+                onChangeText={setDescription}
+                placeholder="Tell us what happened, who was involved, and what the main points of disagreement are..."
+                multiline
+                numberOfLines={4}
+                style={styles.textArea}
+                error={descriptionError}
+              />
+            </View>
+
+            <View style={styles.inputGroup}>
+              <Text style={styles.label}>Number of participants</Text>
+              <Input
+                value={participants}
+                onChangeText={setParticipants}
+                placeholder="2"
+                keyboardType="numeric"
+                hint="Including yourself"
+              />
+            </View>
+          </View>
+        </Card>
+
+        <View style={styles.sessionTypeSection}>
+          <Text style={styles.sectionTitle}>Choose Session Type</Text>
+          <View style={styles.sessionTypes}>
+            {sessionTypes.map((type) => {
+              const IconComponent = type.icon;
+              const isSelected = sessionType === type.id;
+              
+              return (
+                <TouchableOpacity
+                  key={type.id}
+                  style={[
+                    styles.sessionTypeCard,
+                    isSelected && styles.sessionTypeCardSelected
+                  ]}
+                  onPress={() => setSessionType(type.id as any)}
+                >
+                  <View style={[
+                    styles.sessionTypeIcon,
+                    { backgroundColor: isSelected ? type.color : Colors.background.tertiary }
+                  ]}>
+                    <IconComponent 
+                      size={24} 
+                      color={isSelected ? Colors.text.inverse : type.color} 
+                    />
+                  </View>
+                  <Text style={[
+                    styles.sessionTypeTitle,
+                    isSelected && styles.sessionTypeTextSelected
+                  ]}>
+                    {type.title}
+                  </Text>
+                  <Text style={[
+                    styles.sessionTypeDescription,
+                    isSelected && styles.sessionTypeTextSelected
+                  ]}>
+                    {type.description}
+                  </Text>
+                </TouchableOpacity>
+              );
+            })}
+          </View>
+        </View>
+
+        <Card style={styles.previewCard}>
+          <View style={styles.previewHeader}>
+            <Text style={styles.previewTitle}>Session Preview</Text>
+          </View>
+          <View style={styles.previewContent}>
+            <View style={styles.previewItem}>
+              <Text style={styles.previewLabel}>Title:</Text>
+              <Text style={styles.previewValue}>
+                {title || 'Untitled Session'}
+              </Text>
+            </View>
+            <View style={styles.previewItem}>
+              <Text style={styles.previewLabel}>Type:</Text>
+              <Text style={styles.previewValue}>
+                {sessionTypes.find(t => t.id === sessionType)?.title}
+              </Text>
+            </View>
+            <View style={styles.previewItem}>
+              <Text style={styles.previewLabel}>Participants:</Text>
+              <Text style={styles.previewValue}>{participants}</Text>
+            </View>
+          </View>
+        </Card>
+
+        <Button
+          title="Start Session"
+          onPress={handleStartSession}
+          size="large"
+          fullWidth
+          style={styles.startButton}
+        />
+      </ScrollView>
+    </ResponsiveLayout>
+  );
+}
+
+const styles = StyleSheet.create({
+  container: {
+    flex: 1,
+  },
+  
+  header: {
+    marginBottom: Spacing.xl,
+    paddingTop: Spacing.md,
+  },
+  
+  title: {
+    ...Typography.styles.h2,
+    color: Colors.text.primary,
+    marginBottom: Spacing.sm,
+  },
+  
+  subtitle: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+    lineHeight: 22,
+  },
+  
+  formCard: {
+    marginBottom: Spacing.xl,
+  },
+  
+  form: {
+    gap: Spacing.lg,
+  },
+  
+  inputGroup: {
+    gap: Spacing.sm,
+  },
+  
+  label: {
+    ...Typography.styles.label,
+    color: Colors.text.primary,
+  },
+  
+  required: {
+    color: Colors.error,
+  },
+  
+  textArea: {
+    minHeight: 100,
+  },
+  
+  sessionTypeSection: {
+    marginBottom: Spacing.xl,
+  },
+  
+  sectionTitle: {
+    ...Typography.styles.h5,
+    color: Colors.text.primary,
+    marginBottom: Spacing.md,
+  },
+  
+  sessionTypes: {
+    gap: Spacing.md,
+  },
+  
+  sessionTypeCard: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    padding: Spacing.md,
+    backgroundColor: Colors.background.primary,
+    borderRadius: 12,
+    borderWidth: 2,
+    borderColor: Colors.border.light,
+  },
+  
+  sessionTypeCardSelected: {
+    borderColor: Colors.primary[500],
+    backgroundColor: Colors.primary[50],
+  },
+  
+  sessionTypeIcon: {
+    width: 48,
+    height: 48,
+    borderRadius: 24,
+    justifyContent: 'center',
+    alignItems: 'center',
+    marginRight: Spacing.md,
+  },
+  
+  sessionTypeTitle: {
+    ...Typography.styles.body,
+    color: Colors.text.primary,
+    fontWeight: '600',
+    flex: 1,
+    marginBottom: Spacing.xs,
+  },
+  
+  sessionTypeDescription: {
+    ...Typography.styles.caption,
+    color: Colors.text.secondary,
+    flex: 2,
+  },
+  
+  sessionTypeTextSelected: {
+    color: Colors.primary[700],
+  },
+  
+  previewCard: {
+    marginBottom: Spacing.xl,
+    backgroundColor: Colors.background.tertiary,
+  },
+  
+  previewHeader: {
+    marginBottom: Spacing.md,
+  },
+  
+  previewTitle: {
+    ...Typography.styles.h6,
+    color: Colors.text.primary,
+  },
+  
+  previewContent: {
+    gap: Spacing.sm,
+  },
+  
+  previewItem: {
+    flexDirection: 'row',
+    justifyContent: 'space-between',
+    alignItems: 'center',
+  },
+  
+  previewLabel: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+    flex: 1,
+  },
+  
+  previewValue: {
+    ...Typography.styles.body,
+    color: Colors.text.primary,
+    fontWeight: '600',
+    flex: 2,
+    textAlign: 'right',
+  },
+  
+  startButton: {
+    marginBottom: Spacing.xl,
+  },
+});
diff --git a/app/(main)/session/live.tsx b/app/(main)/session/live.tsx
new file mode 100644
index 0000000..07d15e3
--- /dev/null
+++ b/app/(main)/session/live.tsx
@@ -0,0 +1,461 @@
+import React, { useState, useEffect } from 'react';
+import {
+  View,
+  Text,
+  StyleSheet,
+  ScrollView,
+  TouchableOpacity,
+  Alert,
+} from 'react-native';
+import { router } from 'expo-router';
+import { 
+  Pause, 
+  Square, 
+  Users, 
+  Clock, 
+  Brain,
+  Heart,
+  TrendingUp,
+  MessageCircle
+} from 'lucide-react-native';
+
+import { ResponsiveLayout } from '../../../components/layout/ResponsiveLayout';
+import { Card } from '../../../components/ui/Card';
+import { Button } from '../../../components/ui/Button';
+import { VoiceButton } from '../../../components/session/VoiceButton';
+import { Colors } from '../../../constants/Colors';
+import { Typography } from '../../../constants/Typography';
+import { Spacing } from '../../../constants/Spacing';
+
+export default function LiveSessionScreen() {
+  const [isListening, setIsListening] = useState(false);
+  const [isProcessing, setIsProcessing] = useState(false);
+  const [sessionTime, setSessionTime] = useState(0);
+  const [conflictLevel, setConflictLevel] = useState(3); // 1-5 scale
+  const [currentEmotion, setCurrentEmotion] = useState('neutral');
+  const [aiInsight, setAiInsight] = useState('');
+  const [transcription, setTranscription] = useState('');
+
+  // Mock session data
+  const sessionData = {
+    title: 'Family Discussion',
+    participants: 2,
+    type: 'Solo Practice',
+  };
+
+  // Mock emotions data
+  const emotions = {
+    calm: 0.7,
+    frustrated: 0.3,
+    understanding: 0.5,
+    defensive: 0.2,
+  };
+
+  useEffect(() => {
+    // Session timer
+    const timer = setInterval(() => {
+      setSessionTime(prev => prev + 1);
+    }, 1000);
+
+    return () => clearInterval(timer);
+  }, []);
+
+  const formatTime = (seconds: number) => {
+    const mins = Math.floor(seconds / 60);
+    const secs = seconds % 60;
+    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
+  };
+
+  const handleVoicePress = () => {
+    if (isListening) {
+      setIsListening(false);
+      setIsProcessing(true);
+      
+      // Simulate processing
+      setTimeout(() => {
+        setIsProcessing(false);
+        setTranscription('I understand that this situation is challenging for everyone involved...');
+        setAiInsight('Try acknowledging the other person\'s perspective before sharing your own.');
+      }, 2000);
+    } else {
+      setIsListening(true);
+    }
+  };
+
+  const handlePauseSession = () => {
+    Alert.alert(
+      'Pause Session',
+      'Would you like to pause this session? You can resume it later.',
+      [
+        { text: 'Cancel', style: 'cancel' },
+        { text: 'Pause', onPress: () => router.back() },
+      ]
+    );
+  };
+
+  const handleEndSession = () => {
+    Alert.alert(
+      'End Session',
+      'Are you sure you want to end this session? This will take you to the summary.',
+      [
+        { text: 'Cancel', style: 'cancel' },
+        { text: 'End Session', onPress: () => router.push('/(main)/session/summary') },
+      ]
+    );
+  };
+
+  const getConflictLevelColor = (level: number) => {
+    if (level <= 2) return Colors.conflict.low;
+    if (level <= 3) return Colors.conflict.moderate;
+    return Colors.conflict.high;
+  };
+
+  const getEmotionColor = (emotion: string) => {
+    switch (emotion) {
+      case 'calm':
+        return Colors.emotion.calm;
+      case 'frustrated':
+        return Colors.emotion.tension;
+      case 'understanding':
+        return Colors.emotion.positive;
+      case 'defensive':
+        return Colors.emotion.conflict;
+      default:
+        return Colors.emotion.neutral;
+    }
+  };
+
+  return (
+    <ResponsiveLayout>
+      <View style={styles.container}>
+        {/* Header */}
+        <View style={styles.header}>
+          <View style={styles.sessionInfo}>
+            <Text style={styles.sessionTitle}>{sessionData.title}</Text>
+            <View style={styles.sessionMeta}>
+              <View style={styles.metaItem}>
+                <Clock size={16} color={Colors.text.tertiary} />
+                <Text style={styles.metaText}>{formatTime(sessionTime)}</Text>
+              </View>
+              <View style={styles.metaItem}>
+                <Users size={16} color={Colors.text.tertiary} />
+                <Text style={styles.metaText}>{sessionData.participants} participants</Text>
+              </View>
+            </View>
+          </View>
+          
+          <View style={styles.headerActions}>
+            <TouchableOpacity onPress={handlePauseSession} style={styles.headerButton}>
+              <Pause size={20} color={Colors.text.secondary} />
+            </TouchableOpacity>
+            <TouchableOpacity onPress={handleEndSession} style={styles.headerButton}>
+              <Square size={20} color={Colors.error} />
+            </TouchableOpacity>
+          </View>
+        </View>
+
+        {/* Real-time Metrics */}
+        <View style={styles.metricsRow}>
+          <Card style={styles.metricCard}>
+            <View style={styles.metricHeader}>
+              <TrendingUp size={20} color={getConflictLevelColor(conflictLevel)} />
+              <Text style={styles.metricLabel}>Conflict Level</Text>
+            </View>
+            <View style={styles.conflictMeter}>
+              <View style={styles.conflictMeterBackground}>
+                <View 
+                  style={[
+                    styles.conflictMeterFill,
+                    { 
+                      width: `${(conflictLevel / 5) * 100}%`,
+                      backgroundColor: getConflictLevelColor(conflictLevel)
+                    }
+                  ]} 
+                />
+              </View>
+              <Text style={styles.conflictLevelText}>{conflictLevel}/5</Text>
+            </View>
+          </Card>
+
+          <Card style={styles.metricCard}>
+            <View style={styles.metricHeader}>
+              <Heart size={20} color={Colors.emotion.positive} />
+              <Text style={styles.metricLabel}>Emotions</Text>
+            </View>
+            <View style={styles.emotionsContainer}>
+              {Object.entries(emotions).map(([emotion, intensity]) => (
+                <View key={emotion} style={styles.emotionBar}>
+                  <Text style={styles.emotionLabel}>{emotion}</Text>
+                  <View style={styles.emotionMeter}>
+                    <View 
+                      style={[
+                        styles.emotionFill,
+                        { 
+                          width: `${intensity * 100}%`,
+                          backgroundColor: getEmotionColor(emotion)
+                        }
+                      ]} 
+                    />
+                  </View>
+                </View>
+              ))}
+            </View>
+          </Card>
+        </View>
+
+        {/* Voice Interface */}
+        <View style={styles.voiceSection}>
+          <VoiceButton
+            isListening={isListening}
+            isProcessing={isProcessing}
+            onPress={handleVoicePress}
+          />
+          
+          <Text style={styles.voiceInstruction}>
+            {isListening 
+              ? 'Listening... Speak naturally about the situation'
+              : isProcessing
+              ? 'Processing your input...'
+              : 'Tap to start speaking'
+            }
+          </Text>
+        </View>
+
+        {/* AI Insights */}
+        {aiInsight && (
+          <Card style={styles.insightCard}>
+            <View style={styles.insightHeader}>
+              <Brain size={20} color={Colors.primary[500]} />
+              <Text style={styles.insightTitle}>AI Insight</Text>
+            </View>
+            <Text style={styles.insightText}>{aiInsight}</Text>
+          </Card>
+        )}
+
+        {/* Transcription */}
+        {transcription && (
+          <Card style={styles.transcriptionCard}>
+            <View style={styles.transcriptionHeader}>
+              <MessageCircle size={20} color={Colors.text.secondary} />
+              <Text style={styles.transcriptionTitle}>Recent Input</Text>
+            </View>
+            <Text style={styles.transcriptionText}>{transcription}</Text>
+          </Card>
+        )}
+
+        {/* Session Controls */}
+        <View style={styles.controls}>
+          <Button
+            title="Pause Session"
+            onPress={handlePauseSession}
+            variant="outline"
+            style={styles.controlButton}
+          />
+          <Button
+            title="End Session"
+            onPress={handleEndSession}
+            variant="danger"
+            style={styles.controlButton}
+          />
+        </View>
+      </View>
+    </ResponsiveLayout>
+  );
+}
+
+const styles = StyleSheet.create({
+  container: {
+    flex: 1,
+    paddingTop: Spacing.md,
+  },
+  
+  header: {
+    flexDirection: 'row',
+    justifyContent: 'space-between',
+    alignItems: 'flex-start',
+    marginBottom: Spacing.xl,
+  },
+  
+  sessionInfo: {
+    flex: 1,
+  },
+  
+  sessionTitle: {
+    ...Typography.styles.h4,
+    color: Colors.text.primary,
+    marginBottom: Spacing.sm,
+  },
+  
+  sessionMeta: {
+    flexDirection: 'row',
+    gap: Spacing.md,
+  },
+  
+  metaItem: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: Spacing.xs,
+  },
+  
+  metaText: {
+    ...Typography.styles.caption,
+    color: Colors.text.tertiary,
+  },
+  
+  headerActions: {
+    flexDirection: 'row',
+    gap: Spacing.sm,
+  },
+  
+  headerButton: {
+    padding: Spacing.sm,
+    borderRadius: 8,
+    backgroundColor: Colors.background.tertiary,
+  },
+  
+  metricsRow: {
+    flexDirection: 'row',
+    gap: Spacing.md,
+    marginBottom: Spacing.xl,
+  },
+  
+  metricCard: {
+    flex: 1,
+    padding: Spacing.md,
+  },
+  
+  metricHeader: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: Spacing.sm,
+    marginBottom: Spacing.md,
+  },
+  
+  metricLabel: {
+    ...Typography.styles.label,
+    color: Colors.text.secondary,
+  },
+  
+  conflictMeter: {
+    alignItems: 'center',
+    gap: Spacing.sm,
+  },
+  
+  conflictMeterBackground: {
+    width: '100%',
+    height: 8,
+    backgroundColor: Colors.border.light,
+    borderRadius: 4,
+    overflow: 'hidden',
+  },
+  
+  conflictMeterFill: {
+    height: '100%',
+    borderRadius: 4,
+  },
+  
+  conflictLevelText: {
+    ...Typography.styles.body,
+    color: Colors.text.primary,
+    fontWeight: '600',
+  },
+  
+  emotionsContainer: {
+    gap: Spacing.sm,
+  },
+  
+  emotionBar: {
+    gap: Spacing.xs,
+  },
+  
+  emotionLabel: {
+    ...Typography.styles.caption,
+    color: Colors.text.secondary,
+    textTransform: 'capitalize',
+  },
+  
+  emotionMeter: {
+    height: 4,
+    backgroundColor: Colors.border.light,
+    borderRadius: 2,
+    overflow: 'hidden',
+  },
+  
+  emotionFill: {
+    height: '100%',
+    borderRadius: 2,
+  },
+  
+  voiceSection: {
+    alignItems: 'center',
+    marginBottom: Spacing.xl,
+    paddingVertical: Spacing.xl,
+  },
+  
+  voiceInstruction: {
+    ...Typography.styles.body,
+    color: Colors.text.secondary,
+    textAlign: 'center',
+    marginTop: Spacing.lg,
+    maxWidth: 250,
+  },
+  
+  insightCard: {
+    marginBottom: Spacing.lg,
+    backgroundColor: Colors.primary[50],
+    borderWidth: 1,
+    borderColor: Colors.primary[200],
+  },
+  
+  insightHeader: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: Spacing.sm,
+    marginBottom: Spacing.sm,
+  },
+  
+  insightTitle: {
+    ...Typography.styles.label,
+    color: Colors.primary[700],
+    fontWeight: '600',
+  },
+  
+  insightText: {
+    ...Typography.styles.body,
+    color: Colors.primary[800],
+    lineHeight: 22,
+  },
+  
+  transcriptionCard: {
+    marginBottom: Spacing.xl,
+    backgroundColor: Colors.background.tertiary,
+  },
+  
+  transcriptionHeader: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: Spacing.sm,
+    marginBottom: Spacing.sm,
+  },
+  
+  transcriptionTitle: {
+    ...Typography.styles.label,
+    color: Colors.text.secondary,
+  },
+  
+  transcriptionText: {
+    ...Typography.styles.transcription,
+    color: Colors.text.primary,
+  },
+  
+  controls: {
+    flexDirection: 'row',
+    gap: Spacing.md,
+    marginTop: 'auto',
+    paddingBottom: Spacing.xl,
+  },
+  
+  controlButton: {
+    flex: 1,
+  },
+});
diff --git a/components/session/VoiceButton.tsx b/components/session/VoiceButton.tsx
new file mode 100644
index 0000000..5d6db9b
--- /dev/null
+++ b/components/session/VoiceButton.tsx
@@ -0,0 +1,130 @@
+import React from 'react';
+import { TouchableOpacity, View, StyleSheet, Animated } from 'react-native';
+import { Mic, MicOff, Loader } from 'lucide-react-native';
+import { Colors } from '../../constants/Colors';
+import { Spacing } from '../../constants/Spacing';
+
+export interface VoiceButtonProps {
+  isListening: boolean;
+  isProcessing: boolean;
+  disabled?: boolean;
+  onPress: () => void;
+  size?: 'small' | 'medium' | 'large';
+}
+
+export const VoiceButton: React.FC<VoiceButtonProps> = ({
+  isListening,
+  isProcessing,
+  disabled = false,
+  onPress,
+  size = 'large',
+}) => {
+  const getButtonSize = () => {
+    switch (size) {
+      case 'small':
+        return 60;
+      case 'medium':
+        return 80;
+      case 'large':
+        return Spacing.component.voiceButtonSize;
+      default:
+        return Spacing.component.voiceButtonSize;
+    }
+  };
+
+  const getIconSize = () => {
+    switch (size) {
+      case 'small':
+        return 24;
+      case 'medium':
+        return 32;
+      case 'large':
+        return 48;
+      default:
+        return 48;
+    }
+  };
+
+  const buttonSize = getButtonSize();
+  const iconSize = getIconSize();
+
+  const getButtonColor = () => {
+    if (disabled) return Colors.voice.inactive;
+    if (isProcessing) return Colors.voice.processing;
+    if (isListening) return Colors.voice.listening;
+    return Colors.voice.speaking;
+  };
+
+  const getIcon = () => {
+    if (isProcessing) {
+      return <Loader size={iconSize} color={Colors.text.inverse} />;
+    }
+    if (disabled) {
+      return <MicOff size={iconSize} color={Colors.text.inverse} />;
+    }
+    return <Mic size={iconSize} color={Colors.text.inverse} />;
+  };
+
+  return (
+    <TouchableOpacity
+      style={[
+        styles.button,
+        {
+          width: buttonSize,
+          height: buttonSize,
+          borderRadius: buttonSize / 2,
+          backgroundColor: getButtonColor(),
+        },
+        disabled && styles.disabled,
+        isListening && styles.listening,
+      ]}
+      onPress={onPress}
+      disabled={disabled || isProcessing}
+      activeOpacity={0.8}
+    >
+      <View style={styles.iconContainer}>
+        {getIcon()}
+      </View>
+      
+      {isListening && (
+        <View style={[styles.pulse, { width: buttonSize + 20, height: buttonSize + 20 }]} />
+      )}
+    </TouchableOpacity>
+  );
+};
+
+const styles = StyleSheet.create({
+  button: {
+    justifyContent: 'center',
+    alignItems: 'center',
+    shadowColor: Colors.text.primary,
+    shadowOffset: { width: 0, height: 4 },
+    shadowOpacity: 0.3,
+    shadowRadius: 8,
+    elevation: 8,
+  },
+  
+  iconContainer: {
+    justifyContent: 'center',
+    alignItems: 'center',
+  },
+  
+  disabled: {
+    opacity: 0.5,
+    shadowOpacity: 0.1,
+    elevation: 2,
+  },
+  
+  listening: {
+    shadowColor: Colors.voice.listening,
+    shadowOpacity: 0.4,
+  },
+  
+  pulse: {
+    position: 'absolute',
+    borderRadius: 1000,
+    borderWidth: 2,
+    borderColor: Colors.voice.listening,
+    opacity: 0.3,
+  },
+});
-- 
2.39.5

