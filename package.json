{"name": "understand.me", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export --platform web", "build:web": "expo export --platform web", "preview": "npx serve dist", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@elevenlabs/react": "^0.1.7", "@expo-google-fonts/inter": "^0.4.1", "@expo/metro-runtime": "^5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.50.2", "ai": "^4.3.16", "expo": "53.0.15", "expo-av": "~15.1.7", "expo-battery": "^9.1.4", "expo-brightness": "^13.1.4", "expo-camera": "~16.1.10", "expo-dev-client": "^5.2.2", "expo-document-picker": "^13.1.6", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.6", "expo-media-library": "^17.1.7", "expo-router": "~5.1.2", "expo-splash-screen": "^0.30.9", "expo-status-bar": "^2.2.3", "hume": "^0.11.4", "lucide-react-native": "^0.525.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "voyageai": "^0.0.4", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.27.7", "@react-native-community/eslint-config": "^3.2.0", "@types/react": "~19.0.14", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "typescript": "^5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false}}}, "private": true}