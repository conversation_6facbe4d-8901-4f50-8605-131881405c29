import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Animated,
  Dimensions,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';

import { OnboardingStep } from './components/onboarding/OnboardingStep';
import { VoiceButton } from './components/session/VoiceButton';
import { Button } from './components/ui/Button';
import { Card } from './components/ui/Card';
import { ResponsiveLayout } from './components/layout/ResponsiveLayout';

import { Colors } from './constants/Colors';
import { Typography } from './constants/Typography';
import { Spacing } from './constants/Spacing';

import { useAuthStore } from './stores/authStore';
import { aiOrchestrationEngine } from './services/ai/orchestrationEngine';
import { useElevenLabsConversation } from './services/elevenlabs/conversationalAI';

export default function TestComponent() {
  const { width } = Dimensions.get('window');
  const [isListening, setIsListening] = useState(false);
  const [conversationActive, setConversationActive] = useState(false);
  const [agentMessage, setAgentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [pulseAnim] = useState(new Animated.Value(1));

  // ElevenLabs conversation hook
  const conversation = useElevenLabsConversation(
    undefined,
    () => {
      console.log('Connected to Udine for voice intro');
      setConversationActive(true);
      startVoiceIntroduction();
    },
    () => {
      console.log('Disconnected from Udine');
      setConversationActive(false);
    },
    (error) => {
      console.error('Conversation error:', error);
      setConversationActive(false);
    },
    (message) => {
      console.log('Received message:', message);
      if (message.type === 'agent_response') {
        setAgentMessage(message.text);
      }
    }
  );

  const startVoiceIntroduction = async () => {
    try {
      setIsLoading(true);
      
      const result = await aiOrchestrationEngine.startVoiceIntroduction();
      
      if (result.agentResponse) {
        setAgentMessage(result.agentResponse.text);
      }
    } catch (error) {
      console.error('Failed to start voice introduction:', error);
      setAgentMessage("Hello! I'm Udine, your AI companion for understanding and resolving conflicts. Let's start by getting to know each other through voice.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoicePress = () => {
    if (!conversationActive) {
      conversation?.startSession();
    } else {
      setIsListening(!isListening);
      // Note: startRecording/stopRecording methods may not exist in current ElevenLabs API
      console.log(isListening ? 'Stopping listening...' : 'Starting to listen...');
    }
  };

  const handleContinue = () => {
    router.push('/(auth)/onboarding/personality');
  };

  const handleSkip = () => {
    router.push('/(auth)/onboarding/personality');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ResponsiveLayout>
        <OnboardingStep
          currentStep={2}
          totalSteps={5}
          title="Voice Introduction"
          subtitle="Meet Udine, your AI companion"
          onBack={() => router.back()}
          onSkip={handleSkip}
          showNextButton={false}
        >
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <Card style={styles.voiceCard}>
              <View style={styles.voiceHeader}>
                <Text style={styles.voiceTitle}>Meet Udine</Text>
                <Text style={styles.voiceSubtitle}>
                  Your empathetic AI companion for conflict resolution
                </Text>
              </View>

              {agentMessage ? (
                <View style={styles.messageContainer}>
                  <View style={styles.messageBubble}>
                    <Text style={styles.messageText}>{agentMessage}</Text>
                  </View>
                  <Text style={styles.speakerLabel}>Udine</Text>
                </View>
              ) : null}

              <View style={styles.voiceContainer}>
                <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
                  <VoiceButton
                    isListening={isListening}
                    isProcessing={isLoading}
                    onPress={handleVoicePress}
                    size="large"
                    disabled={isLoading}
                  />
                </Animated.View>
                
                <Text style={styles.voiceInstructions}>
                  {!conversationActive 
                    ? "Tap to start conversation"
                    : isListening 
                      ? "Listening... Say hello!"
                      : "Tap to speak"
                  }
                </Text>
              </View>

              <View style={styles.statusContainer}>
                <View style={[
                  styles.statusIndicator,
                  {
                    backgroundColor: conversationActive
                      ? Colors.emotion.growth
                      : Colors.text.tertiary,
                  }
                ]} />
                <Text style={styles.statusText}>
                  {conversationActive ? 'Connected' : 'Disconnected'}
                </Text>
              </View>
            </Card>

            <Card style={styles.benefitsCard}>
              <Text style={styles.benefitsTitle}>Why Voice Matters</Text>
              <View style={styles.benefitsList}>
                <View style={styles.benefitItem}>
                  <Text style={styles.benefitIcon}>🎯</Text>
                  <Text style={styles.benefitText}>
                    More natural and expressive communication
                  </Text>
                </View>
                <View style={styles.benefitItem}>
                  <Text style={styles.benefitIcon}>💝</Text>
                  <Text style={styles.benefitText}>
                    Emotional nuances captured through tone
                  </Text>
                </View>
                <View style={styles.benefitItem}>
                  <Text style={styles.benefitIcon}>⚡</Text>
                  <Text style={styles.benefitText}>
                    Faster and more intuitive interaction
                  </Text>
                </View>
              </View>
            </Card>

            <Button
              title="Continue to Personality Assessment"
              onPress={handleContinue}
              variant="primary"
              style={styles.continueButton}
            />
          </ScrollView>
        </OnboardingStep>
      </ResponsiveLayout>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  content: {
    flex: 1,
  },
  voiceCard: {
    padding: Spacing.xl,
    marginBottom: Spacing.lg,
  },
  voiceHeader: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  voiceTitle: {
    ...Typography.styles.h2,
    color: Colors.text.primary,
    marginBottom: Spacing.xs,
  },
  voiceSubtitle: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  messageContainer: {
    marginBottom: Spacing.xl,
  },
  messageBubble: {
    backgroundColor: Colors.primary[100],
    padding: Spacing.md,
    borderRadius: 16,
    marginBottom: Spacing.xs,
  },
  messageText: {
    ...Typography.styles.body,
    color: Colors.text.primary,
    lineHeight: 22,
  },
  speakerLabel: {
    ...Typography.styles.bodySmall,
    color: Colors.text.secondary,
    fontWeight: '600',
    marginLeft: Spacing.sm,
  },
  voiceContainer: {
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  voiceInstructions: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.sm,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    ...Typography.styles.bodySmall,
    color: Colors.text.secondary,
  },
  benefitsCard: {
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  benefitsTitle: {
    ...Typography.styles.h3,
    color: Colors.text.primary,
    marginBottom: Spacing.md,
  },
  benefitsList: {
    gap: Spacing.md,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
  },
  benefitIcon: {
    fontSize: 20,
    lineHeight: 24,
  },
  benefitText: {
    ...Typography.styles.body,
    color: Colors.text.secondary,
    flex: 1,
    lineHeight: 22,
  },
  continueButton: {
    marginBottom: Spacing.xl,
  },
});
