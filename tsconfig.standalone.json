{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./*"], "react": ["node_modules/@types/react"], "react-native": ["node_modules/@types/react-native"], "expo-router": ["node_modules/expo-router"], "react-native-safe-area-context": ["node_modules/react-native-safe-area-context"]}, "types": ["react", "react-native"]}, "include": ["*.ts", "*.tsx", "test-*.ts", "test-*.tsx", "test.tsx", "Untitled-*", "Untitled-1"], "exclude": ["node_modules", "dist", "build", ".expo"]}